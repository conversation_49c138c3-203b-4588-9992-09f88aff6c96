{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/group_member_infor/index.vue?5539", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/group_member_infor/index.vue?4a7a", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/group_member_infor/index.vue?62cc", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/group_member_infor/index.vue?f87f", "uni-app:///pagesGoEasy/group_member_infor/index.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/group_member_infor/index.vue?36f7", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/group_member_infor/index.vue?8213"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "pageObj", "senderData", "onLoad", "console", "methods", "openimg", "getData"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACwL;AACxL,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA2sB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACyB/tB;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AAAA,eACA;EACAC;IACA;MACAC;QACAC;MACA;IACA;EACA;EACAC;IACAH;IACAI;IACA;EACA;EACAC;IACAC;IACAC;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAAA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC5CA;AAAA;AAAA;AAAA;AAAk2C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACAt3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesGoEasy/group_member_infor/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesGoEasy/group_member_infor/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=68635031&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=68635031&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"68635031\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesGoEasy/group_member_infor/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=68635031&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\n\t\t<view style=\"height: 20rpx\"></view>\n\t\t<view class=\"flex_r fa_c infor\">\n\t\t\t<view class=\"infor-img-box\" @click=\"openimg(pageObj.member_avatar)\">\n\t\t\t\t<!-- <view class=\"avatar_pendant\" v-if=\"pageObj.senderData\">\n\t\t\t\t\t<image class=\"img\" :src=\"pageObj.senderData.avatar_pendant\" mode=\"aspectFill\"></image>\n\t\t\t\t</view> -->\n\t\t\t\t<view class=\"infor-img\">\n\t\t\t\t\t<image class=\"img\" src=\"\" mode=\"aspectFill\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"flex1 infor-r\">\n\t\t\t\t<view class=\"flex_r fa_c infor-r-name\">\n\t\t\t\t\t<view class=\"text_34 bold_\">xxxxxx</view>\n\t\t\t\t\t<!-- <view class=\"text_20 color__ label\">{{ pageObj.group_name }}</view> -->\n\t\t\t\t</view>\n\t\t\t\t<view class=\"text_26 color__\">群昵称：xxx</view>\n\t\t\t\t<view class=\"text_26 color__\">ID：xxxxx</view>\r\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport { jsonUrl, openimg } from '@/utils/index';\nlet data = null;\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tpageObj: {\n\t\t\t\tsenderData: {}\n\t\t\t}\n\t\t};\n\t},\n\tonLoad(e) {\n\t\tdata = jsonUrl(e);\n\t\tconsole.log(data);\n\t\tthis.getData();\n\t},\n\tmethods: {\n\t\topenimg,\n\t\tasync getData() {},\n\t}\n};\n</script>\n\n<style scoped lang=\"scss\">\n.infor {\n\tbox-sizing: border-box;\n\tpadding: 30rpx;\n\twidth: 100%;\n\t.infor-img-box {\n\t\tposition: relative;\r\n\t\tmargin-right: 20rpx;\n\t\t.infor-img {\n\t\t\twidth: 150rpx;\n\t\t\theight: 150rpx;\n\t\t\tborder-radius: 20rpx;\n\t\t\toverflow: hidden;\n\t\t\tbackground-color: #f1f1f1;\n\t\t}\n\t\t.avatar_pendant {\n\t\t\tposition: absolute;\n\t\t\tz-index: 3;\n\t\t\ttop: -2px;\n\t\t\tleft: -2px;\n\t\t\tbottom: -2px;\n\t\t\tright: -2px;\n\t\t}\n\t}\n\n\t.infor-r {\n\t\t.infor-r-name {\n\t\t\twidth: 100%;\n\t\t\t.label {\n\t\t\t\tbox-sizing: border-box;\n\t\t\t\tpadding: 0 10rpx;\n\t\t\t\t// height: 38rpx;\n\t\t\t\tline-height: 38rpx;\n\t\t\t\tmargin-left: 20rpx;\n\t\t\t\tborder-radius: 30rpx;\n\t\t\t\tborder: 1px solid #999;\n\t\t\t}\n\t\t}\n\t}\n}\n</style>\n", "import mod from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=68635031&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=68635031&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754983153415\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}