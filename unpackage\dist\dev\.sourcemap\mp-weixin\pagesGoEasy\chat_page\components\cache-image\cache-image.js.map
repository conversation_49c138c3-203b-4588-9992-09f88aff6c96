{"version": 3, "sources": ["webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/components/cache-image/cache-image.vue?c786", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/components/cache-image/cache-image.vue?b0c9", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/components/cache-image/cache-image.vue?53f0", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/components/cache-image/cache-image.vue?1059", "uni-app:///pagesGoEasy/chat_page/components/cache-image/cache-image.vue"], "names": ["name", "props", "src", "type", "default", "mstyle", "ext", "data", "img_url", "watch", "handler", "n", "isCache", "immediate", "computed", "filename", "methods", "uni", "filePath", "success", "fail", "createDownload", "dtask"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;;;AAG1D;AAC8L;AAC9L,gBAAgB,yLAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA+uB,CAAgB,irBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;eCKnwB;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;EACA;EACAG;IACA;MACAC;IACA;EACA;EACAC;IACAP;MACAQ;QAAA;UAAA;UAAA;YAAA;cAAA;gBAAA;kBAAA,IACAC;oBAAA;oBAAA;kBAAA;kBAAA;gBAAA;kBAAA;kBAAA,OACA;gBAAA;kBAAAC;kBACA;oBACA;kBACA;oBACA;oBACA;kBACA;gBAAA;gBAAA;kBAAA;cAAA;YAAA;UAAA;QAAA,CACA;QAAA;UAAA;QAAA;QAAA;MAAA;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;EACA;EACAC;IACA;IACAJ;MACA;QACAK;UACAC;UACAC;YACA;cACA;YACA;YACA;UACA;UACAC;YACA;UACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA,2CACA,UACA;QACAN;MACA;MACA;MACA;QACA;UACA;UACA;UACA;UACA;QACA;UACA;QACA;MACA,EACA;MACAO;MACA;MACA;IACA;EACA;AACA;AAAA,2B", "file": "pagesGoEasy/chat_page/components/cache-image/cache-image.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./cache-image.vue?vue&type=template&id=4eb87bc0&\"\nvar renderjs\nimport script from \"./cache-image.vue?vue&type=script&lang=js&\"\nexport * from \"./cache-image.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesGoEasy/chat_page/components/cache-image/cache-image.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cache-image.vue?vue&type=template&id=4eb87bc0&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cache-image.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cache-image.vue?vue&type=script&lang=js&\"", "<template>\n\t<image class=\"img\" :src=\"img_url\" :style=\"mstyle\" mode=\"aspectFill\"></image>\n</template>\n\n<script>\nexport default {\n\tname: 'cache-image',\n\tprops: {\n\t\tsrc: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\tmstyle: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\text: {\n\t\t\ttype: String,\n\t\t\tdefault: 'gif'\n\t\t}\n\t},\n\tdata() {\n\t\treturn {\n\t\t\timg_url: ''\n\t\t};\n\t},\n\twatch: {\n\t\tsrc: {\n\t\t\thandler: async function (n) {\n\t\t\t\tif (!n) return;\n\t\t\t\tlet isCache = await this.isCache(this.filename);\n\t\t\t\tif (isCache) {\n\t\t\t\t\tthis.img_url = this.filename;\n\t\t\t\t} else {\n\t\t\t\t\tthis.img_url = n;\n\t\t\t\t\tthis.createDownload();\n\t\t\t\t}\n\t\t\t},\n\t\t\timmediate: true\n\t\t}\n\t},\n\tcomputed: {\n\t\tfilename() {\n\t\t\tlet filename = `_doc/IM_emoji_pack/${this.src.replace(/\\./g, '_').replace(/\\//g, '_')}.${this.ext}`;\n\t\t\treturn filename;\n\t\t}\n\t},\n\tmethods: {\n\t\t// 判断是否已经缓存\n\t\tisCache(filePath) {\n\t\t\treturn new Promise((r) => {\n\t\t\t\tuni.getFileInfo({\n\t\t\t\t\tfilePath,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.errMsg === 'getFileInfo:ok') {\n\t\t\t\t\t\t\treturn r(true);\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn r(false);\n\t\t\t\t\t},\n\t\t\t\t\tfail: (e) => {\n\t\t\t\t\t\treturn r(false);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t});\n\t\t},\n\t\t// 下载存储\n\t\tcreateDownload() {\n\t\t\tlet dtask = plus.downloader.createDownload(\n\t\t\t\tthis.src,\n\t\t\t\t{\n\t\t\t\t\tfilename: this.filename\n\t\t\t\t},\n\t\t\t\t//\n\t\t\t\t(download, status) => {\n\t\t\t\t\tif (status == 200) {\n\t\t\t\t\t\tlet image = download.options.filename; //设置的名字\n\t\t\t\t\t\t// this.image = download.filename; //实际生成的名字\n\t\t\t\t\t\t// 将本地URL路径转换成平台绝对路径\n\t\t\t\t\t\tthis.img_url = plus.io.convertLocalFileSystemURL(image);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.img_url = src;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t);\n\t\t\tdtask.start();\n\t\t\t// 下载进度\n\t\t\t// dtask.addEventListener('statechanged', function (task, status) {});\n\t\t}\n\t}\n};\n</script>\n"], "sourceRoot": ""}