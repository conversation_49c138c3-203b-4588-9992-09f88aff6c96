{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/message/message.vue?6f9d", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/message/message.vue?7aaf", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/message/message.vue?ba47", "uni-app:///pagesHuYunIm/pages/message/message.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/message/message.vue?a4db", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/message/message.vue?2605"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "selfUserId", "selfAvatar", "userMap", "groupId", "chatTitle", "isOnline", "list", "page", "pageSize", "loading", "loadend", "scrollTop", "inputMode", "inputText", "inputFocus", "keyboardHeight", "showEmojiPanel", "showMorePanel", "isRecording", "recordingText", "voiceButtonText", "content", "messageType", "recordStart", "top", "focus", "isMounted", "music", "mqttClientInstance", "computed", "messageList", "onUnload", "mqttClient", "onLoad", "console", "uni", "title", "onShow", "RecordApp", "audio", "goBack", "showChatMenu", "itemList", "success", "initMqttConnection", "store", "onConnect", "onMessage", "onReconnect", "onError", "onEnd", "handleMqttMessage", "chatMsg", "sendReadReceipt", "id", "formatMessages", "messages", "formattedMessages", "message", "showCreateTime", "lastTimestamp", "isSelfMessage", "getMessageClass", "getUserAvatar", "getUserNickname", "showNickname", "formatTime", "hour", "minute", "date", "switchToVoiceMode", "switchToTextMode", "handleInputFocus", "setTimeout", "handleInputBlur", "handleInput", "sendTextMessage", "icon", "userId", "msgType", "localMsgId", "status", "generateLocalMsgId", "toggleEmojiPanel", "toggleMorePanel", "scrollToBottom", "loadMoreMessages", "startRecording", "stopRecording", "cancelRecording", "handleMessageClick", "playVoiceMessage", "previewImage", "urls", "current", "showMessageMenu", "items", "copyMessage", "withdrawMessage", "showUserProfile", "getVoiceIcon", "getVoiceDuration", "chooseImage", "count", "sourceType", "<PERSON><PERSON><PERSON><PERSON>", "chooseVideo", "chooseFile", "clearChatHistory", "showGroupMembers", "url", "showGroupSettings", "onClickChild", "send", "self", "confirmText", "cancelText", "showCancel", "recorder<PERSON>anager", "scope", "fail", "tempFile<PERSON>ath", "audioSrc", "userType", "avatar", "src", "idEnd", "res", "err", "item", "opts", "protocolId", "protocolVersion", "clientId", "username", "password", "clean", "on", "clearInterval", "filePath", "name", "formData", "header", "opt", "file", "success<PERSON>allback", "<PERSON><PERSON><PERSON><PERSON>", "e", "audioTrackSet", "noiseSuppression", "echoCancellation", "autoGainControl", "type", "sampleRate", "bitRate", "onProcess", "onProcess_renderjs", "onProcessBefore_renderjs", "takeoffEncodeChunk", "takeoffEncodeChunk_renderjs", "start_renderjs", "stop_renderjs", "set", "compatibleCanvas", "width", "height", "arrayBuffer"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AAC2L;AAC3L,gBAAgB,yLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAA4tB,CAAgB,6qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;AC4LhvB;AACA;AACA;AAGA;AACA;AACA;AAIA;AACA;AACA;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AAAA;EAGAC;IACA;MACA;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MAEA;MACAC;MAAA;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;IACA;EACA;EAEAC;IACA;AACA;AACA;IACAC;MACA;IACA;EACA;EAEAC;IACA;IACA;MACAC;IACA;;IAEA;IACA;MACA;MACA;IACA;EACA;EAEAC;IACAC;IACAA;IACA;IACA;IACAC;MAAAC;IAAA;IACA;IACA;IACAF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAG;IACA;IACA;EACA;AAAA,oEACA;EACAP;IACAI;IACA;EACA;AACA,qFACA;EACA;IACA;EACA;AACA,uFACA;EACA;EACA;EACAI;EACA;IAAAC;EAAA;AACA;EAEA;EAEA;AACA;AACA;EACAC;IACAL;EACA;EAEA;AACA;AACA;EACAM;IAAA;IACAN;MACAO;MACAC;QACA;UACA;YACA;YACA;UACA;YACA;YACA;UACA;YACA;YACA;QAAA;MAEA;IACA;EACA;EAEA;AACA;AACA;EACAC;IAAA;IACA;IACAZ;IACA;IACA,+CACAa,0CACAA,oDACAA,+CACAA,gCACAA,0CACA,MACA;;IAEA;IACA;MACAC;QACAZ;QACA;MACA;MACAa;QACA;MACA;MACAC;QACAd;QACA;MACA;MACAe;QACAf;QACA;MACA;MACAgB;QACAhB;QACA;MACA;IACA;;IAEA;IACA;EACA;EAEA;AACA;AACA;EACAiB;IACA;MACA;;MAEA;MACA;QACAC;MACA;;MAEA;MACA;QACA;QACA;;QAEA;QACA;MACA;QACA;MAAA;IAEA;EACA;EAEA;AACA;AACA;EACAC;IACA;MACA;MACA;QACAC;QACAnD;MACA;MACA6B;IACA;EACA;EAEA;EAEA;AACA;AACA;EACAuB;IAAA;IACA;IACA;IAEAC;MACA;MACA;QACAC,uDACAC;UACAC;QAAA,GACA;QACAC;MACA;QACAH,uDACAC;UACAC;QAAA,GACA;MACA;IACA;IAEA;EACA;EAEA;AACA;AACA;EACAE;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;EACA;EAEA;AACA;AACA;EACAC;IAAA;IACA;EACA;EAEA;AACA;AACA;EACAC;IAAA;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;IACA;IAEA;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;IACA;;IAEA;IACA;MACA;QACAC;QACAC;MACA;IACA;;IAEA;IACA;IACA;MACA,OACA,QACAC;QACAF;QACAC;MACA;IAEA;;IAEA;IACA,OACAC,mCACA,MACAA;MACAF;MACAC;IACA;EAEA;EAEA;EAEA;AACA;AACA;EACAE;IACA;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;EACA;EAEA;AACA;AACA;EACAC;IAAA;IACA;IACA;IACA;;IAEA;IACAC;MACA;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;MACAzC;QACAC;QACAyC;MACA;MACA;IACA;IAEA;MACAxD;MACAyD;MACA3E;MACA4E;MACAC;MACAC;IACA;;IAEA;IACA;;IAEA;IACA;IACAjD;;IAEA;IACA;IACA;;IAEA;IACA;EACA;EAEA;AACA;AACA;EACAkD;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;IACA;EACA;EAEA;AACA;AACA;EACAC;IAAA;IACA;MACA;MACA;IACA;EACA;EAEA;AACA;AACA;EACAC;IAAA;IACA;MACA;MACAb;QACA;QACA;MACA;IACA;EACA;EAEA;EAEA;AACA;AACA;EACAc;IACA;IACA;IACA;IACA;;IAEA;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;IACA;;IAEA;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;IACA;;IAEA;IACA;MACAnD,kBACA;QACAJ;MACA,GACA;QACAA;MACA,EACA;IACA;EACA;EAEA;EAEA;AACA;AACA;EACAwD;IACA;MACA;IACA;MACA;IACA;EACA;EAEA;AACA;AACA;EACAC;IAAA;IACA;IACA;MACA;IACA;;IAEA;IACA;;IAEA;IACA;IACA;IAEA;MACAzD;IACA;IAEA;MACAA;MACA;MACA;IACA;IAEA;MACAA;MACA;MACA;IACA;IAEA;EACA;EAEA;AACA;AACA;EACA0D;IACAzD;MACA0D;MACAC;IACA;EACA;EAEA;AACA;AACA;EACAC;IAAA;IACA;IAEA;MACAC;IACA;IAEA7D;MACAO;MACAC;QACA;UACA;YACA;YACA;UACA;YACA;cACA;YACA;YACA;QAAA;MAEA;IACA;EACA;EAEA;AACA;AACA;EACAsD;IACA;MACA9D;QACApC;QACA4C;UACAR;YACAC;YACAyC;UACA;QACA;MACA;IACA;EACA;EAEA;AACA;AACA;EACAqB;IACA;IACA;MACA5C;MACA0B;IACA;IAEAhD;EACA;EAEA;AACA;AACA;EACAmE;IACAjE;IACA;EACA;EAEA;AACA;AACA;EACAkE;IACA;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;MACA;IACA;IACA;EACA;EAEA;EAEA;AACA;AACA;EACAC;IAAA;IACAnE;MACAoE;MACAC;MACA7D;QACA;MACA;IACA;EACA;EAEA;AACA;AACA;EACA8D;IAAA;IACAtE;MACAoE;MACAC;MACA7D;QACA;MACA;IACA;EACA;EAEA;AACA;AACA;EACA+D;IACAvE;MACAqE;MACA7D;QACAT;QACA;MACA;IACA;EACA;EAEA;AACA;AACA;EACAyE;IAaAxE;MACAC;MACAyC;IACA;EAEA;EAEA;EAEA;AACA;AACA;EACA+B;IAAA;IACAzE;MACAC;MACAf;MACAsB;QACA;UACA;UACAR;YACAC;YACAyC;UACA;QACA;MACA;IACA;EACA;EAEA;AACA;AACA;EACAgC;IACA;IACA1E;MACA2E;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA5E;MACA2E;IACA;EACA;EAEA;AACA;AACA;EACAE;IACA9E;IACA;EACA;EAEA;EAEA+E;IACA;MACA9E;QACAC;QACAyC;MACA;MACA;IACA;IAEA;MACAxD;MACAyD;MACA3E;MACA4E;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;EACA;AAAA,iFAEA;EACA;EACA5C;IACAqE;IACA7D;MACAuE;IACA;EACA;AACA,wFAEA;EACA;EACAhF;AACA,0EAEAnC;EAAA;EACA;IACA;IACA;IACA;IACA;MACA;MACA;IACA;EACA;AACA,4EAEA;EAAA;EACAoC;IACAC;IACAf;IACA8F;IACAC;IACAzE;MACA;QACAR;UACAQ;YACA;cACAT;cACA;YACA;cACA;cACAxC;gBACA0C;gBACAf;gBACAgG;gBACAF;cACA;YACA;UACA;QACA;MACA;IACA;EACA;AACA,gFAEA;EAAA;EACA;EACA;EACAhF;IACAQ;MACA;MACA;QACA;QACA;UACA;QACA;UACA;UACA;UACA;UACA2E;UACAA;YACA;UACA;;UAEA;UACAA;YACApF;YACAC;cACA0C;cACAzC;YACA;YACA;UACA;QACA;MACA;QACA;QACAD;UACAoF;UACA5E;YACA;YACA;UACA;UACA6E;YACA;AACA;AACA;AACA;AACA;YACA;cACArF;gBACAC;gBACAf;gBACA8F;gBACAE;gBACA1E;cACA;YACA;cACA;cACA;YACA;UACA;QACA;MACA;IACA;EACA;AACA,4EAEA;EAAA;EACA;EACA;EACA2E;EACAA;IACApF;IACA;MAAAuF;IACA;IAEA;MACApG;MACAqG;MACAC;MACAC;MACAtG;IACA;IACA;MACA;IACA;EACA;AACA,kEAGAuG;EACA;EACA;EACA;EACA;IACA3F;EACA;EACA;IACA;IACAA;EACA;EACA;IACAA;EACA;AACA,4EAEA;EAAA;IAAA;EAAA;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA4F;YAAA;YAAA,KAEA;cAAA;cAAA;YAAA;YAAA;UAAA;YAAA,KACA;cAAA;cAAA;YAAA;YAAA;UAAA;YACA;YACA/H;cACAQ;cACAC;cACAL;YACA;YACA;cACAJ;YACA;YAAA;YAAA,OACA;UAAA;YAAA;YAAA;YAAAgI;YAAAC;YACA;cACA;cACA;gBACAD;kBACA;oBACA7F;oBACA+F;kBACA;gBACA;cACA;gBACA/F;gBACA;cACA;;cACA;cACA;cACAA;cACA;gBACAuC;kBACA;gBACA;cACA;YACA;YACA;cACAvC;YACA;YAAA;YAAA;UAAA;YAAA;YAAA;UAAA;YAAA;YAIA;YAAA;UAAA;UAAA;YAAA;QAAA;MAAA;IAAA;EAAA;AAEA,kFACA;EAAA;EAAA;IAAA;IAAA;MAAA;QAAA;UAAA;YACAgF;YACAgB;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;YACA;YACAtB;YACAA,gBACAuB;cACA;cACAC;cACAxB;gBACAA;gBACAhF;cACA;cACAgF;gBACA;kBACA;kBACA;kBACA;gBAAA;cAEA;YACA,GACAuB;cACA;YAAA,CACA,EACAA;cACA;YAAA,CACA,EACAA;cACAvG;cACA;YACA,GACAuG;cACAvG;cACA;cACA;gBACA;gBACA;kBACAkB;gBACA;gBACA;kBACA8D;kBACA;kBACAA;gBACA;kBACA;kBACA;oBACAhF;kBACA;oBACAA;oBACA;kBACA;gBACA;cACA;YACA;UAAA;UAAA;YAAA;QAAA;MAAA;IAAA;EAAA;AACA,gFACAyG;EAAA;EACAxG;IACA2E;IAAA;IACA6B;IAAA;IACAC;IAAA;IACAC;IACAC;MACA;MACA;IACA;;IACAnG;MACA;MACA;QACA;;QAEA;UACAtB;UACAyD;UACA3E;UACA4E;QACA;QACA;QACA7C;QACA;QACA;QACA;QACA;QACA;QACA;QACAA;MACA;QACAC;UACAC;UACAyC;QACA;MACA;IACA;IACA2C;MACArF;QACAC;QACAyC;MACA;MACA3C;IACA;EACA;AACA,8EACA6G;EACA5G;IACA2E;IAAA;IACAkC;IAAA;IACAJ;IAAA;IACAC;IACAC;MACA;MACA;IACA;;IACAnG;MACAT;MACA;MACA;QACA;QACA+G;MACA;QACAC;MACA;IACA;IACA1B;MACAtF;MACAgH;IACA;EACA;AACA,gGACAC;EAAA;EACA;EACA1E;IACA;EACA;AACA,kFACA1E;EACAmC;AACA,gFACA;EACA;EACA;AACA,wEACA;EACA;AACA,wEAEA;EAAA;EACAC;EACA;EACA;EACA;;EAEAG;IAAA8G;MAAAC;MAAAC;MAAAC;IAAA;EAAA;;EAEAjH;EACAA,+BACA;IACAJ;IACA;IACA;EACA,GACA;IACA;MACA;MACA;MACA;IAAA;IAEAA;EACA,EACA;AACA,4EAGA;EAAA;EACA;EACA;EACA;EACA;IACAsH;IACAC;IACAC;IAAA;IACA;AACA;AACA;IACAC;MACA;;MAEA;MACA;;MAEA;;MAEA;;MAGA;AACA;AACA;AACA;IACA;;IACAC,g4DAUA;IACAC,urBAGA;IAEAC,2BACA,OACA,SAKA;IACAC,oCACA,gBAGA;IAEAC,8eAGA;IACAC;EAIA;EAEA3H;EACAA,mBACA4H,KACA;IACAhI;IACA;IACA;;IAEA;IACA;IACA;IACAI,2BACA,SACA,kJAIA;MACA;QAAA6H;QAAAC;QAAAC;MAAA;IACA,EACA;EACA,GACA;IACAnI;EACA,EACA;AACA,4EAGA;EACA;IACAI;IACAJ;EACA;AACA,8EAEA;EACA;IACAI;IACAJ;EACA;AACA,0EAGA;EACA;EACA;EACAI,kBACA;IACA;IACA;;IAEA;;IAEA;;IA4BA;;IAEAA,8BACA,gBACAgI,aACA;MACApI;MACA;IACA,GACA;MACAA;IACA,EACA;EAEA,GACA;IACAA;EACA,EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChhDA;AAAA;AAAA;AAAA;AAA+3C,CAAgB,ouCAAG,EAAC,C;;;;;;;;;;;ACAn5C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesHuYunIm/pages/message/message.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesHuYunIm/pages/message/message.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./message.vue?vue&type=template&id=0b926c58&scoped=true&\"\nvar renderjs\nimport script from \"./message.vue?vue&type=script&lang=js&\"\nexport * from \"./message.vue?vue&type=script&lang=js&\"\nimport style0 from \"./message.vue?vue&type=style&index=0&id=0b926c58&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0b926c58\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesHuYunIm/pages/message/message.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=template&id=0b926c58&scoped=true&\"", "var render = function () {}\nvar staticRenderFns = []\nvar recyclableRender\nvar components\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"chat-container\">\n    <!-- 顶部导航栏 -->\n    <view class=\"chat-header\">\n      <view class=\"header-left\" @click=\"goBack\">\n        <text class=\"back-icon\">‹</text>\n      </view>\n      <view class=\"header-center\">\n        <text class=\"chat-title\">{{ chatTitle }}</text>\n        <text class=\"online-status\" v-if=\"isOnline\">在线</text>\n      </view>\n      <view class=\"header-right\" @click=\"showChatMenu\">\n        <text class=\"more-icon\">⋯</text>\n      </view>\n    </view>\n\n    <!-- 消息列表 -->\n    <scroll-view\n      class=\"message-list\"\n      scroll-y\n      scroll-with-animation\n      :scroll-top=\"scrollTop\"\n      @scrolltoupper=\"loadMoreMessages\"\n      :enable-back-to-top=\"true\"\n    >\n      <!-- 加载更多指示器 -->\n      <view class=\"load-more\" v-if=\"loading\">\n        <text class=\"loading-text\">加载中...</text>\n      </view>\n\n      <!-- 消息内容 -->\n      <view class=\"message-content\">\n        <block v-for=\"(item, index) in messageList\" :key=\"item.id || index\">\n          <!-- 时间分隔线 -->\n          <view class=\"time-divider\" v-if=\"item.showCreateTime\">\n            <text class=\"time-text\">{{ formatTime(item.createTime) }}</text>\n          </view>\n\n          <!-- 消息气泡 -->\n          <view class=\"message-item\" :class=\"getMessageClass(item)\">\n            <!-- 左侧头像（对方消息） -->\n            <view class=\"avatar-container\" v-if=\"!isSelfMessage(item)\">\n              <image :src=\"getUserAvatar(item.userId)\" class=\"avatar\" mode=\"aspectFill\" @click=\"showUserProfile(item.userId)\" />\n            </view>\n\n            <!-- 消息主体 -->\n            <view class=\"message-body\">\n              <!-- 昵称（对方消息） -->\n              <view class=\"nickname\" v-if=\"!isSelfMessage(item) && showNickname(item, index)\">\n                {{ getUserNickname(item.userId) }}\n              </view>\n\n              <!-- 消息内容 -->\n              <view class=\"message-bubble\" @click=\"handleMessageClick(item)\" @longpress=\"showMessageMenu(item)\">\n                <!-- 文本消息 -->\n                <view class=\"text-content\" v-if=\"item.msgType === 'text'\">\n                  {{ item.content }}\n                </view>\n\n                <!-- 图片消息 -->\n                <view class=\"image-content\" v-else-if=\"item.msgType === 'image'\">\n                  <image :src=\"item.content\" mode=\"aspectFill\" class=\"message-image\" @click=\"previewImage(item.content)\" />\n                </view>\n\n                <!-- 语音消息 -->\n                <view class=\"voice-content\" v-else-if=\"item.msgType === 'voice'\" :class=\"{ playing: item.isPlaying }\">\n                  <image :src=\"getVoiceIcon(item)\" class=\"voice-icon\" :class=\"{ 'voice-animation': item.isPlaying }\" />\n                  <text class=\"voice-duration\">{{ getVoiceDuration(item) }}</text>\n                </view>\n\n                <!-- 消息状态 -->\n                <view class=\"message-status\" v-if=\"isSelfMessage(item)\">\n                  <text class=\"status-icon\" v-if=\"item.status === 'sending'\">⏳</text>\n                  <text class=\"status-icon\" v-else-if=\"item.status === 'failed'\">❌</text>\n                  <text class=\"status-icon\" v-else-if=\"item.status === 'sent'\">✓</text>\n                  <text class=\"status-icon read\" v-else-if=\"item.status === 'read'\">✓✓</text>\n                </view>\n              </view>\n            </view>\n\n            <!-- 右侧头像（自己的消息） -->\n            <view class=\"avatar-container\" v-if=\"isSelfMessage(item)\">\n              <image :src=\"selfAvatar\" class=\"avatar\" mode=\"aspectFill\" />\n            </view>\n          </view>\n        </block>\n      </view>\n\n      <!-- 底部占位 -->\n      <view class=\"bottom-placeholder\"></view>\n    </scroll-view>\n\n    <!-- 输入工具栏 -->\n    <view class=\"input-toolbar\" :style=\"{ bottom: keyboardHeight + 'px' }\">\n      <!-- 语音录制模式 -->\n      <view class=\"voice-mode\" v-if=\"inputMode === 'voice'\">\n        <view class=\"voice-controls\">\n          <text class=\"mode-switch\" @click=\"switchToTextMode\">文</text>\n          <view\n            class=\"voice-button\"\n            :class=\"{ recording: isRecording }\"\n            @touchstart=\"startRecording\"\n            @touchend=\"stopRecording\"\n            @touchcancel=\"cancelRecording\"\n          >\n            <text class=\"voice-text\">{{ voiceButtonText }}</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 文本输入模式 -->\n      <view class=\"text-mode\" v-else>\n        <view class=\"input-controls\">\n          <!-- 语音按钮 -->\n          <text class=\"voice-btn\" @click=\"switchToVoiceMode\">🎤</text>\n\n          <!-- 文本输入框 -->\n          <view class=\"input-wrapper\">\n            <textarea\n              v-model=\"inputText\"\n              class=\"text-input\"\n              placeholder=\"输入消息...\"\n              :focus=\"inputFocus\"\n              :auto-height=\"true\"\n              :max-height=\"120\"\n              @focus=\"handleInputFocus\"\n              @blur=\"handleInputBlur\"\n              @input=\"handleInput\"\n              @confirm=\"sendTextMessage\"\n            />\n          </view>\n\n          <!-- 表情按钮 -->\n          <text class=\"emoji-btn\" @click=\"toggleEmojiPanel\">😊</text>\n\n          <!-- 更多按钮 -->\n          <text class=\"more-btn\" @click=\"toggleMorePanel\" v-if=\"!inputText.trim()\">+</text>\n\n          <!-- 发送按钮 -->\n          <view class=\"send-btn\" v-if=\"inputText.trim()\" @click=\"sendTextMessage\">\n            <text class=\"send-text\">发送</text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 表情面板 -->\n    <view class=\"emoji-panel\" v-if=\"showEmojiPanel\">\n      <!-- 表情内容 -->\n    </view>\n\n    <!-- 更多功能面板 -->\n    <view class=\"more-panel\" v-if=\"showMorePanel\">\n      <view class=\"more-grid\">\n        <view class=\"more-item\" @click=\"chooseImage\">\n          <text class=\"more-icon\">📷</text>\n          <text class=\"more-text\">相册</text>\n        </view>\n        <view class=\"more-item\" @click=\"takePhoto\">\n          <text class=\"more-icon\">📸</text>\n          <text class=\"more-text\">拍照</text>\n        </view>\n        <view class=\"more-item\" @click=\"chooseVideo\">\n          <text class=\"more-icon\">🎬</text>\n          <text class=\"more-text\">视频</text>\n        </view>\n        <view class=\"more-item\" @click=\"chooseFile\">\n          <text class=\"more-icon\">📁</text>\n          <text class=\"more-text\">文件</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 语音录制动画 -->\n    <view class=\"recording-overlay\" v-if=\"isRecording\">\n      <view class=\"recording-animation\">\n        <view class=\"wave-container\">\n          <view class=\"wave\" v-for=\"i in 5\" :key=\"i\" :style=\"{ animationDelay: i * 0.1 + 's' }\"></view>\n        </view>\n        <text class=\"recording-text\">{{ recordingText }}</text>\n        <text class=\"recording-tip\">松开发送，上滑取消</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\n// 录音相关导入\nimport Recorder from 'recorder-core'\nimport RecordApp from 'recorder-core/src/app-support/app'\nimport '@/uni_modules/Recorder-UniCore/app-uni-support.js'\n\n// #ifdef H5 || MP-WEIXIN\nimport 'recorder-core/src/engine/mp3'\nimport 'recorder-core/src/engine/mp3-engine'\nimport 'recorder-core/src/extensions/waveview'\n// #endif\n\n// 新的MQTT工具包\nimport mqttClient from '@/utils/mqttClient.js'\nimport { createUserInfo, createChatMessage, MESSAGE_TYPES, TOPIC_TEMPLATES } from '@/utils/mqttConfig.js'\nimport mqtt from '@/utils/mqtt.min.js'\n\n// API和工具\nimport { msglist } from '@/api/public.js'\nimport store from '@/store'\nimport Cache from '@/utils/cache.js'\nimport { SplitArray } from '@/utils'\n\n// 录音管理器\nconst recorderManager = uni.getRecorderManager()\n\nexport default {\n  data() {\n    return {\n      // 用户信息\n      selfUserId: store.state.app.userInfo.userId,\n      selfAvatar: store.state.app.userInfo.avatar,\n      userMap: {},\n\n      // 聊天信息\n      groupId: '',\n      chatTitle: '',\n      isOnline: false,\n\n      // 消息列表\n      list: [],\n      page: 1,\n      pageSize: 50,\n      loading: false,\n      loadend: false,\n\n      // 滚动相关\n      scrollTop: 0,\n\n      // 输入相关\n      inputMode: 'text', // 'text' | 'voice'\n      inputText: '',\n      inputFocus: false,\n      keyboardHeight: 0,\n\n      // 面板状态\n      showEmojiPanel: false,\n      showMorePanel: false,\n\n      // 语音录制\n      isRecording: false,\n      recordingText: '正在录音...',\n      voiceButtonText: '按住 说话',\n\n      // 旧版兼容\n      content: '',\n      messageType: 'text',\n      recordStart: false,\n      top: 0,\n      focus: false,\n      isMounted: false,\n      music: null,\n\n      // MQTT相关（将使用新的工具包）\n      mqttClientInstance: null\n    }\n  },\n\n  computed: {\n    /**\n     * 格式化后的消息列表\n     */\n    messageList() {\n      return this.formatMessages(this.list)\n    }\n  },\n\n  onUnload() {\n    // 断开MQTT连接\n    if (this.mqttClientInstance) {\n      mqttClient.disconnect()\n    }\n\n    // 清理音频播放\n    if (this.music) {\n      this.music.destroy()\n      this.music = null\n    }\n  },\n\n  onLoad(options) {\n    console.log('页面参数:', options)\n    console.log('用户信息:', store.state.app.userInfo)\n    // 设置页面标题和基本信息\n    this.chatTitle = options.name || '聊天'\n    uni.setNavigationBarTitle({ title: this.chatTitle })\n    // 初始化用户映射\n    this.userMap = JSON.parse(Cache.get('userMap') || '{}')\n    console.log('用户映射:', this.userMap)\n    // 设置群组ID\n    this.groupId = options.groupId\n    // 兼容旧版本\n    this._friendAvatar = options.avatar\n    this._selfAvatar = store.state.app.userInfo.avatar\n    // 加载消息数据\n    this.loadData()\n    // 初始化MQTT连接\n    this.initMqttConnection()\n  },\n  onShow() {\n    //onShow可能比mounted先执行，页面可能还未准备好\n    if (this.isMounted) RecordApp.UniPageOnShow(this)\n  },\n  computed: {\n    messageList() {\n      console.log('🚀 ~ messageList ~ this.formatMessages(this.list):', this.formatMessages(this.list))\n      return this.formatMessages(this.list)\n    }\n  },\n  onHide() {\n    if (this._innerAudioContext) {\n      this._innerAudioContext.stop()\n    }\n  },\n  mounted() {\n    this.isMounted = true\n    //App的renderjs必须调用的函数，传入当前模块this\n    RecordApp.UniRenderjsRegister(this)\n    const stream = navigator.mediaDevices.getUserMedia({ audio: true })\n  },\n  methods: {\n    // ==================== 新增的UI交互方法 ====================\n\n    /**\n     * 返回上一页\n     */\n    goBack() {\n      uni.navigateBack()\n    },\n\n    /**\n     * 显示聊天菜单\n     */\n    showChatMenu() {\n      uni.showActionSheet({\n        itemList: ['清空聊天记录', '查看群成员', '群设置'],\n        success: (res) => {\n          switch (res.tapIndex) {\n            case 0:\n              this.clearChatHistory()\n              break\n            case 1:\n              this.showGroupMembers()\n              break\n            case 2:\n              this.showGroupSettings()\n              break\n          }\n        }\n      })\n    },\n\n    /**\n     * 初始化MQTT连接\n     */\n    initMqttConnection() {\n      // 设置MQTT库\n      mqttClient.setMqttLib(mqtt)\n      // 创建用户信息\n      const userInfo = createUserInfo(\n        store.state.app.userInfo.userId,\n        store.state.app.userInfo.nickname || '用户',\n        store.state.app.userInfo.channelCode,\n        store.state.app.token,\n        store.state.app.userInfo.avatar,\n        'DEV'\n      )\n\n      // 设置回调函数\n      const callbacks = {\n        onConnect: () => {\n          console.log('MQTT连接成功')\n          this.isOnline = true\n        },\n        onMessage: (topic, mqttMsg) => {\n          this.handleMqttMessage(mqttMsg)\n        },\n        onReconnect: () => {\n          console.log('MQTT重连中...')\n          this.isOnline = false\n        },\n        onError: (error) => {\n          console.error('MQTT连接错误:', error)\n          this.isOnline = false\n        },\n        onEnd: () => {\n          console.log('MQTT连接已断开')\n          this.isOnline = false\n        }\n      }\n\n      // 连接MQTT\n      this.mqttClientInstance = mqttClient.connect(userInfo, callbacks)\n    },\n\n    /**\n     * 处理MQTT消息\n     */\n    handleMqttMessage(mqttMsg) {\n      if (mqttMsg.command === MESSAGE_TYPES.CHAT_MSG) {\n        const chatMsg = mqttMsg.data\n\n        // 设置用户昵称\n        if (this.userMap.hasOwnProperty(chatMsg.userId)) {\n          chatMsg.nickname = this.userMap[chatMsg.userId].nickname\n        }\n\n        // 如果是当前群组的消息\n        if (this.groupId === chatMsg.groupId) {\n          this.list.push(chatMsg)\n          this.scrollToBottom()\n\n          // 发送已读回执\n          this.sendReadReceipt(chatMsg)\n        } else {\n          // 其他群组的消息通知\n        }\n      }\n    },\n\n    /**\n     * 发送已读回执\n     */\n    sendReadReceipt(message) {\n      if (message.id) {\n        const readTopic = `/chat/server/${this.selfUserId}/read`\n        const readData = {\n          id: message.id,\n          groupId: this.groupId\n        }\n        mqttClient.publish(readTopic, readData)\n      }\n    },\n\n    // ==================== 消息相关方法 ====================\n\n    /**\n     * 格式化消息列表，添加时间显示逻辑\n     */\n    formatMessages(messages, thresholdInMinutes = 10) {\n      const formattedMessages = []\n      let lastTimestamp = null\n\n      messages.forEach((message) => {\n        const currentTimestamp = new Date(message.createTime).getTime()\n        if (!lastTimestamp || (currentTimestamp - lastTimestamp) / (1000 * 60) > thresholdInMinutes) {\n          formattedMessages.push({\n            ...message,\n            showCreateTime: true\n          })\n          lastTimestamp = currentTimestamp\n        } else {\n          formattedMessages.push({\n            ...message,\n            showCreateTime: false\n          })\n        }\n      })\n\n      return formattedMessages\n    },\n\n    /**\n     * 判断是否为自己发送的消息\n     */\n    isSelfMessage(message) {\n      return message.userId === this.selfUserId\n    },\n\n    /**\n     * 获取消息样式类名\n     */\n    getMessageClass(message) {\n      return this.isSelfMessage(message) ? 'self-message' : 'friend-message'\n    },\n\n    /**\n     * 获取用户头像\n     */\n    getUserAvatar(userId) {\n      return this.userMap[userId]?.avatar || '/static/default-avatar.png'\n    },\n\n    /**\n     * 获取用户昵称\n     */\n    getUserNickname(userId) {\n      return this.userMap[userId]?.nickname || '未知用户'\n    },\n\n    /**\n     * 是否显示昵称\n     */\n    showNickname(message, index) {\n      // 群聊中显示昵称，私聊中不显示\n      // 连续的同一用户消息只显示第一条的昵称\n      if (index === 0) return true\n\n      const prevMessage = this.messageList[index - 1]\n      return prevMessage.userId !== message.userId\n    },\n\n    /**\n     * 格式化时间显示\n     */\n    formatTime(timestamp) {\n      const date = new Date(timestamp)\n      const now = new Date()\n      const diff = now.getTime() - date.getTime()\n\n      // 今天\n      if (diff < 24 * 60 * 60 * 1000 && now.getDate() === date.getDate()) {\n        return date.toLocaleTimeString('zh-CN', {\n          hour: '2-digit',\n          minute: '2-digit'\n        })\n      }\n\n      // 昨天\n      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)\n      if (yesterday.getDate() === date.getDate()) {\n        return (\n          '昨天 ' +\n          date.toLocaleTimeString('zh-CN', {\n            hour: '2-digit',\n            minute: '2-digit'\n          })\n        )\n      }\n\n      // 更早\n      return (\n        date.toLocaleDateString('zh-CN') +\n        ' ' +\n        date.toLocaleTimeString('zh-CN', {\n          hour: '2-digit',\n          minute: '2-digit'\n        })\n      )\n    },\n\n    // ==================== 输入相关方法 ====================\n\n    /**\n     * 切换到语音模式\n     */\n    switchToVoiceMode() {\n      this.inputMode = 'voice'\n      this.inputFocus = false\n    },\n\n    /**\n     * 切换到文本模式\n     */\n    switchToTextMode() {\n      this.inputMode = 'text'\n      this.inputFocus = true\n    },\n\n    /**\n     * 处理输入框焦点\n     */\n    handleInputFocus() {\n      this.inputFocus = true\n      this.showEmojiPanel = false\n      this.showMorePanel = false\n\n      // 延迟滚动到底部，等待键盘弹出\n      setTimeout(() => {\n        this.scrollToBottom()\n      }, 300)\n    },\n\n    /**\n     * 处理输入框失焦\n     */\n    handleInputBlur() {\n      this.inputFocus = false\n    },\n\n    /**\n     * 处理输入内容变化\n     */\n    handleInput(e) {\n      this.inputText = e.detail.value\n    },\n\n    /**\n     * 发送文本消息\n     */\n    sendTextMessage() {\n      if (!this.inputText.trim()) {\n        uni.showToast({\n          title: '消息不能为空',\n          icon: 'none'\n        })\n        return\n      }\n\n      const chatMsg = {\n        content: this.inputText.trim(),\n        userId: this.selfUserId,\n        groupId: this.groupId,\n        msgType: 'text',\n        localMsgId: this.generateLocalMsgId(),\n        status: 'sending'\n      }\n\n      // 添加到消息列表\n      this.list.push(chatMsg)\n\n      // 发送MQTT消息\n      const topic = `/chat/server/${this.selfUserId}/msg`\n      mqttClient.publish(topic, chatMsg)\n\n      // 清空输入框\n      this.inputText = ''\n      this.content = '' // 兼容旧版本\n\n      // 滚动到底部\n      this.scrollToBottom()\n    },\n\n    /**\n     * 生成本地消息ID\n     */\n    generateLocalMsgId() {\n      return Date.now() + '_' + Math.random().toString(36).substring(2, 11)\n    },\n\n    /**\n     * 切换表情面板\n     */\n    toggleEmojiPanel() {\n      this.showEmojiPanel = !this.showEmojiPanel\n      this.showMorePanel = false\n      this.inputFocus = false\n    },\n\n    /**\n     * 切换更多功能面板\n     */\n    toggleMorePanel() {\n      this.showMorePanel = !this.showMorePanel\n      this.showEmojiPanel = false\n      this.inputFocus = false\n    },\n\n    /**\n     * 滚动到底部\n     */\n    scrollToBottom() {\n      this.$nextTick(() => {\n        this.scrollTop = this.list.length * 1000\n        this.top = this.list.length * 1000 // 兼容旧版本\n      })\n    },\n\n    /**\n     * 加载更多消息\n     */\n    loadMoreMessages() {\n      if (this.list.length > 0) {\n        this.loadData(this.list[0].id)\n        setTimeout(() => {\n          this.scrollTop = 100\n          this.top = 100 // 兼容旧版本\n        }, 1000)\n      }\n    },\n\n    // ==================== 语音相关方法 ====================\n\n    /**\n     * 开始录音\n     */\n    startRecording() {\n      this.isRecording = true\n      this.recordStart = true // 兼容旧版本\n      this.recordingText = '正在录音...'\n      this.voiceButtonText = '松开 发送'\n\n      // 调用录音权限请求\n      this.recReq()\n    },\n\n    /**\n     * 停止录音\n     */\n    stopRecording() {\n      this.isRecording = false\n      this.recordStart = false // 兼容旧版本\n      this.voiceButtonText = '按住 说话'\n\n      // 调用录音停止\n      this.recStop()\n    },\n\n    /**\n     * 取消录音\n     */\n    cancelRecording() {\n      this.isRecording = false\n      this.recordStart = false // 兼容旧版本\n      this.voiceButtonText = '按住 说话'\n\n      // 取消录音逻辑\n      if (RecordApp.GetCurrentRecOrNull()) {\n        RecordApp.Stop(\n          () => {\n            console.log('录音已取消')\n          },\n          () => {\n            console.log('取消录音失败')\n          }\n        )\n      }\n    },\n\n    // ==================== 消息交互方法 ====================\n\n    /**\n     * 处理消息点击\n     */\n    handleMessageClick(message) {\n      if (message.msgType === 'voice') {\n        this.playVoiceMessage(message)\n      } else if (message.msgType === 'image') {\n        this.previewImage(message.content)\n      }\n    },\n\n    /**\n     * 播放语音消息\n     */\n    playVoiceMessage(message) {\n      // 停止当前播放的音频\n      if (this.music) {\n        this.music.stop()\n      }\n\n      // 设置播放状态\n      this.$set(message, 'isPlaying', true)\n\n      // 创建音频播放器\n      this.music = uni.createInnerAudioContext()\n      this.music.src = message.content.audioSrc || message.content\n\n      this.music.onPlay(() => {\n        console.log('开始播放语音')\n      })\n\n      this.music.onEnded(() => {\n        console.log('语音播放结束')\n        this.$set(message, 'isPlaying', false)\n        this.music = null\n      })\n\n      this.music.onError((error) => {\n        console.error('语音播放错误:', error)\n        this.$set(message, 'isPlaying', false)\n        this.music = null\n      })\n\n      this.music.play()\n    },\n\n    /**\n     * 预览图片\n     */\n    previewImage(imageUrl) {\n      uni.previewImage({\n        urls: [imageUrl],\n        current: imageUrl\n      })\n    },\n\n    /**\n     * 显示消息菜单\n     */\n    showMessageMenu(message) {\n      const items = ['复制']\n\n      if (this.isSelfMessage(message)) {\n        items.push('撤回')\n      }\n\n      uni.showActionSheet({\n        itemList: items,\n        success: (res) => {\n          switch (res.tapIndex) {\n            case 0:\n              this.copyMessage(message)\n              break\n            case 1:\n              if (this.isSelfMessage(message)) {\n                this.withdrawMessage(message)\n              }\n              break\n          }\n        }\n      })\n    },\n\n    /**\n     * 复制消息\n     */\n    copyMessage(message) {\n      if (message.msgType === 'text') {\n        uni.setClipboardData({\n          data: message.content,\n          success: () => {\n            uni.showToast({\n              title: '已复制',\n              icon: 'success'\n            })\n          }\n        })\n      }\n    },\n\n    /**\n     * 撤回消息\n     */\n    withdrawMessage(message) {\n      const withdrawTopic = `/chat/server/${this.selfUserId}/withdraw`\n      const withdrawData = {\n        id: message.id,\n        localMsgId: message.localMsgId\n      }\n\n      mqttClient.publish(withdrawTopic, withdrawData)\n    },\n\n    /**\n     * 显示用户资料\n     */\n    showUserProfile(userId) {\n      console.log('显示用户资料:', userId)\n      // 这里可以跳转到用户资料页面\n    },\n\n    /**\n     * 获取语音图标\n     */\n    getVoiceIcon(message) {\n      const isLeft = !this.isSelfMessage(message)\n      return isLeft ? '/static/icon-voice-left.png' : '/static/icon-voice-right.png'\n    },\n\n    /**\n     * 获取语音时长\n     */\n    getVoiceDuration(message) {\n      if (typeof message.content === 'object' && message.content.msg) {\n        return message.content.msg\n      }\n      return '1\"'\n    },\n\n    // ==================== 多媒体相关方法 ====================\n\n    /**\n     * 选择图片\n     */\n    chooseImage() {\n      uni.chooseImage({\n        count: 1,\n        sourceType: ['album'],\n        success: (res) => {\n          this.uploadImage(res.tempFilePaths[0])\n        }\n      })\n    },\n\n    /**\n     * 拍照\n     */\n    takePhoto() {\n      uni.chooseImage({\n        count: 1,\n        sourceType: ['camera'],\n        success: (res) => {\n          this.uploadImage(res.tempFilePaths[0])\n        }\n      })\n    },\n\n    /**\n     * 选择视频\n     */\n    chooseVideo() {\n      uni.chooseVideo({\n        sourceType: ['album', 'camera'],\n        success: (res) => {\n          console.log('选择视频:', res)\n          // 这里可以添加视频上传逻辑\n        }\n      })\n    },\n\n    /**\n     * 选择文件\n     */\n    chooseFile() {\n      // #ifdef H5\n      const input = document.createElement('input')\n      input.type = 'file'\n      input.onchange = (e) => {\n        const file = e.target.files[0]\n        console.log('选择文件:', file)\n        // 这里可以添加文件上传逻辑\n      }\n      input.click()\n      // #endif\n\n      // #ifndef H5\n      uni.showToast({\n        title: '该平台暂不支持文件选择',\n        icon: 'none'\n      })\n      // #endif\n    },\n\n    // ==================== 群组管理方法 ====================\n\n    /**\n     * 清空聊天记录\n     */\n    clearChatHistory() {\n      uni.showModal({\n        title: '确认清空',\n        content: '确定要清空聊天记录吗？',\n        success: (res) => {\n          if (res.confirm) {\n            this.list = []\n            uni.showToast({\n              title: '已清空',\n              icon: 'success'\n            })\n          }\n        }\n      })\n    },\n\n    /**\n     * 显示群成员\n     */\n    showGroupMembers() {\n      // 这里可以跳转到群成员页面\n      uni.navigateTo({\n        url: `/pages/groupMembers/groupMembers?groupId=${this.groupId}`\n      })\n    },\n\n    /**\n     * 显示群设置\n     */\n    showGroupSettings() {\n      // 这里可以跳转到群设置页面\n      uni.navigateTo({\n        url: `/pages/groupSettings/groupSettings?groupId=${this.groupId}`\n      })\n    },\n\n    /**\n     * 处理通知点击\n     */\n    onClickChild(data) {\n      console.log('点击消息通知:', data)\n      // 可以在这里处理通知点击事件，比如跳转到对应的聊天\n    },\n\n    // ==================== 兼容旧版本的方法 ====================\n\n    send() {\n      if (this.content === '') {\n        uni.showToast({\n          title: '消息不能为空',\n          icon: 'none'\n        })\n        return\n      }\n\n      let chatMsg = {\n        content: this.content,\n        userId: this.selfUserId,\n        groupId: this.groupId,\n        msgType: 'text'\n      }\n      this.list.push(chatMsg)\n\n      this.mqttClient.publish('/chat/server/' + store.state.app.userInfo.userId + '/msg', JSON.stringify(chatMsg))\n      //this.mqttClient.\n      //推送消息到客户端\n      this.content = ''\n      this.scrollToBottom()\n    },\n\n    chooseImage() {\n      let self = this\n      uni.chooseImage({\n        sourceType: ['camera', 'album'],\n        success: (res) => {\n          self.uploadImage(res.tempFilePaths[0])\n        }\n      })\n    },\n\n    scrollToBottom() {\n      this.top = this.list.length * 1000\n      console.log('🚀 ~ scrollToBottom ~ this.top:', this.top)\n    },\n\n    msgClick(data) {\n      if (data.msgType === 'voice') {\n        this.music = uni.createInnerAudioContext() //创建播放器对象\n        this.music.src = data.content.audioSrc // static文件夹下的音频\n        this.music.play() //执行播放\n        this.music.onEnded(() => {\n          //播放结束\n          this.music = null\n        })\n      }\n    },\n\n    authTips() {\n      uni.showModal({\n        title: '提示',\n        content: '您拒绝了麦克风权限，将导致功能不能正常使用，去设置权限？',\n        confirmText: '去设置',\n        cancelText: '取消',\n        success: (res) => {\n          if (res.confirm) {\n            uni.openSetting({\n              success: (res) => {\n                if (res.authSetting['scope.record']) {\n                  console.log('已授权麦克风')\n                  this._recordAuth = true\n                } else {\n                  // 未授权\n                  wx.showModal({\n                    title: '提示',\n                    content: '您未授权麦克风，功能将无法使用',\n                    showCancel: false,\n                    confirmText: '知道了'\n                  })\n                }\n              }\n            })\n          }\n        }\n      })\n    },\n\n    touchstart() {\n      //开始录音\n      const _permission = 'scope.record'\n      uni.getSetting({\n        success: (res) => {\n          // 判断是否有相关权限属性\n          if (res.authSetting.hasOwnProperty(_permission)) {\n            // 属性存在，且为false，用户拒绝过权限\n            if (!res.authSetting[_permission]) {\n              this.authTips()\n            } else {\n              // 已授权\n              this._recordAuth = true\n              // 开始录音\n              recorderManager.start()\n              recorderManager.onStart(() => {\n                this.recordStart = true\n              })\n\n              // 错误回调\n              recorderManager.onError((res) => {\n                console.log('recorder error', res)\n                uni.showToast({\n                  icon: 'none',\n                  title: '系统出错，请重试'\n                })\n                this.recordStart = false\n              })\n            }\n          } else {\n            // 属性不存在，需要授权\n            uni.authorize({\n              scope: _permission,\n              success: () => {\n                // 授权成功\n                this._recordAuth = true\n              },\n              fail: (res) => {\n                /**\n                 * 104 未授权隐私协议\n                 * 用户可能拒绝官方隐私授权弹窗，为了避免过度弹窗打扰用户，开发者再次调用隐私相关接口时，\n                 * 若距上次用户拒绝不足10秒，将不再触发弹窗，直接给到开发者用户拒绝隐私授权弹窗的报错\n                 */\n                if (res.errno === 104) {\n                  uni.showModal({\n                    title: '温馨提示',\n                    content: '您拒绝了隐私协议，请稍后再试',\n                    confirmText: '知道了',\n                    showCancel: false,\n                    success: () => {}\n                  })\n                } else {\n                  // 用户拒绝授权\n                  this.authTips()\n                }\n              }\n            })\n          }\n        }\n      })\n    },\n\n    touchend() {\n      if (!this._recordAuth || !this.recordStart) return\n      //停止录音\n      recorderManager.stop()\n      recorderManager.onStop((res) => {\n        console.log('结束录音', res)\n        const { duration, tempFilePath } = res\n        this.recordStart = false\n\n        this.list.push({\n          content: `语音 ${Math.round(duration / 1000)}''`,\n          audioSrc: tempFilePath,\n          userType: 'self',\n          avatar: this._selfAvatar,\n          messageType: 'voice'\n        })\n        this.$nextTick(() => {\n          this.scrollToBottom()\n        })\n      })\n    },\n\n    //播放声音\n    play(src) {\n      this._innerAudioContext = wx.createInnerAudioContext()\n      this._innerAudioContext.src = src\n      this._innerAudioContext.play()\n      this._innerAudioContext.onPlay(() => {\n        console.log('开始播放')\n      })\n      this._innerAudioContext.onEnded(() => {\n        // 播放完毕，清除音频链接\n        console.log('播放完毕')\n      })\n      this._innerAudioContext.onError((res) => {\n        console.log('audio play error', res)\n      })\n    },\n\n    async loadData(idEnd = '') {\n      try {\n        if (this.loading) return\n        if (this.loadend) return\n        this.loading = true\n        let data = {\n          page: this.page,\n          pageSize: this.pageSize,\n          groupId: this.groupId\n        }\n        if (idEnd) {\n          data.id_end = idEnd\n        }\n        const [res, err] = await msglist(data)\n        if (res) {\n          //this.users = res\n          try {\n            res.forEach((item) => {\n              if (item.msgType === 'voice' && item.content.length > 10) {\n                console.log('🚀 ~ loadData ~ item.msgType:', JSON.parse(item.content))\n                item.content = JSON.parse(item.content)\n              }\n            })\n          } catch (error) {\n            console.log('🚀 ~ loadData ~ error:', error)\n            //TODO handle the exception\n          }\n          this.list = SplitArray(res, this.list)\n          this.loadend = res.length < this.pageSize\n          console.log('🚀 ~  ~ res:', res)\n          if (!idEnd) {\n            setTimeout(() => {\n              this.scrollToBottom()\n            }, 100)\n          }\n        }\n        if (err) {\n          console.log('🚀 ~  ~ err:', err)\n        }\n      } catch (error) {\n        //TODO handle the exception\n      } finally {\n        this.loading = false\n      }\n    },\n    async connectMqtt() {\n      var self = this\n      let opts = {\n        protocolId: 'MQTT',\n        protocolVersion: 4,\n        clientId: store.state.app.userInfo.userId,\n        username: store.state.app.userInfo.channelCode,\n        password: store.state.app.token,\n        clean: false\n      }\n      self.mqttClient = mqtt.connect(store.state.app.userInfo.wsUrl, opts)\n      self.mqttClient\n        .on('connect', function () {\n          //self.logs.push('on connect')\n          clearInterval(self.mqttPingIntetval)\n          self.mqttPingIntetval = setInterval(function () {\n            self.mqttClient.publish('/chat/server/' + store.state.app.userInfo.userId + '/ping', '1')\n            console.log('ping-2')\n          }, 10000)\n          self.mqttClient.subscribe('/chat/client/' + store.state.app.userInfo.userId, function (err) {\n            if (!err) {\n              // client.publish('/chat/server/' + store.state.app.userInfo.userId + '/msg',\n              // \t'{\"groupId\":\"1911787038405492737\",\"msgType\":\"text\",\"content\":\"亭亭白桦\"}'\n              // )\n            }\n          })\n        })\n        .on('reconnect', function () {\n          //self.logs.push('on reconnect')\n        })\n        .on('error', function () {\n          //self.logs.push('on error')\n        })\n        .on('end', function () {\n          console.log('MQTT断开连接-2')\n          //self.logs.push('on end')\n        })\n        .on('message', function (topic, message) {\n          console.log('message.toString', message.toString())\n          let mqttMsg = JSON.parse(message.toString())\n          if (mqttMsg['command'] === 'chatMsg') {\n            let chatMsg = mqttMsg['data']\n            if (self.userMap.hasOwnProperty(chatMsg['userId'])) {\n              chatMsg['nickname'] = self.userMap[chatMsg['userId']]['nickname']\n            }\n            if (self.groupId === chatMsg['groupId']) {\n              self.list.push(chatMsg)\n              //告诉服务器，已读\n              self.mqttClient.publish('/chat/server/' + store.state.app.userInfo.userId + '/read', '{\"groupId\":' + self.groupId + '}')\n            } else {\n              // 消息通知\n              try {\n                console.log('🚀 ~ self:', self)\n              } catch (error) {\n                console.log('🚀 ~ error:', error)\n                //TODO handle the exception\n              }\n            }\n          }\n        })\n    },\n    uploadImage(filePath) {\n      uni.uploadFile({\n        url: '/jeecg-boot/huyun/front/chat/upload', // 替换为你的服务器接口地址\n        filePath: filePath, // 临时文件路径\n        name: 'file', // 后端接收文件的字段名（需与服务器一致）\n        formData: {},\n        header: {\n          'x-access-token': store.state.app.token // 自定义请求头\n          // 注意：不要手动设置 Content-Type，uni-app 会自动处理\n        },\n        success: (res) => {\n          // 上传成功，处理服务器响应\n          if (res.statusCode === 200) {\n            const data = JSON.parse(res.data) // 解析服务器返回的数据\n\n            let chatMsg = {\n              content: data.message,\n              userId: this.selfUserId,\n              groupId: this.groupId,\n              msgType: 'image'\n            }\n            this.list.push(chatMsg)\n            console.log('chatMsgImage', chatMsg)\n            this.mqttClient.publish('/chat/server/' + store.state.app.userInfo.userId + '/msg', JSON.stringify(chatMsg))\n            this.scrollToBottom()\n            // uni.showToast({\n            // \ttitle: '上传成功',\n            // \ticon: 'success'\n            // });\n            console.log('服务器返回:', data)\n          } else {\n            uni.showToast({\n              title: '上传失败',\n              icon: 'none'\n            })\n          }\n        },\n        fail: (err) => {\n          uni.showToast({\n            title: '上传失败',\n            icon: 'none'\n          })\n          console.error('上传失败:', err)\n        }\n      })\n    },\n    uploadFile(opt, successCallback, errorCallback) {\n      uni.uploadFile({\n        url: '/jeecg-boot/huyun/front/chat/upload', // 替换为你的服务器接口地址\n        file: opt.file, // 临时文件路径\n        name: 'file', // 后端接收文件的字段名（需与服务器一致）\n        formData: {},\n        header: {\n          'x-access-token': store.state.app.token // 自定义请求头\n          // 注意：不要手动设置 Content-Type，uni-app 会自动处理\n        },\n        success: (res) => {\n          console.log('🚀 ~ uploadFile ~ res:', res)\n          // 上传成功，处理服务器响应\n          if (res.statusCode === 200) {\n            const data = JSON.parse(res.data) // 解析服务器返回的数据\n            successCallback && successCallback(data.message)\n          } else {\n            errorCallback && errorCallback(res)\n          }\n        },\n        fail: (err) => {\n          console.log('🚀 ~ uploadFile ~ err:', err)\n          errorCallback && errorCallback(err)\n        }\n      })\n    },\n    handleScrolltoupper(e) {\n      this.loadData(this.list[0].id)\n      setTimeout(() => {\n        this.top = 100\n      }, 1000)\n    },\n    onClickChild(data) {\n      console.log('点击消息回调函数', data)\n    },\n    handleText() {\n      this.messageType = 'text'\n      this.focus = true\n    },\n    onBlur() {\n      this.focus = false\n    },\n    //请求录音权限\n    recReq() {\n      uni.startRecord\n      //\n      //编译成App时提供的授权许可（编译成H5、小程序为免费授权可不填写）；如果未填写授权许可，将会在App打开后第一次调用请求录音权限时，弹出“未获得商用授权时，App上仅供测试”提示框\n      //RecordApp.UniAppUseLicense='我已获得UniAppID=*****的商用授权';\n\n      RecordApp.RequestPermission_H5OpenSet = { audioTrackSet: { noiseSuppression: true, echoCancellation: true, autoGainControl: true } } //这个是Start中的audioTrackSet配置，在h5（H5、App+renderjs）中必须提前配置，因为h5中RequestPermission会直接打开录音\n\n      RecordApp.UniWebViewActivate(this) //App环境下必须先切换成当前页面WebView\n      RecordApp.RequestPermission(\n        () => {\n          console.log('已获得录音权限，可以开始录音了')\n          this.recStart()\n          this.recordStart = true\n        },\n        (msg, isUserNotAllow) => {\n          if (isUserNotAllow) {\n            //用户拒绝了录音权限\n            //这里你应当编写代码进行引导用户给录音权限，不同平台分别进行编写\n            //\n          }\n          console.error('请求录音权限失败：' + msg)\n        }\n      )\n    },\n\n    //开始录音\n    recStart() {\n      //Android App如果要后台录音，需要启用后台录音保活服务（iOS不需要），需使用配套原生插件、或使用第三方保活插件\n      //RecordApp.UniNativeUtsPluginCallAsync(\"androidNotifyService\",{ title:\"正在录音\" ,content:\"正在录音中，请勿关闭App运行\" }).then(()=>{...}).catch((e)=>{...}) 注意必须RecordApp.RequestPermission得到权限后调用\n      //录音配置信息\n      var set = {\n        type: 'mp3',\n        sampleRate: 16000,\n        bitRate: 16, //mp3格式，指定采样率hz、比特率kbps，其他参数使用默认配置；注意：是数字的参数必须提供数字，不要用字符串；需要使用的type类型，需提前把格式支持文件加载进来，比如使用wav格式需要提前加载wav.js编码引擎\n        /*,audioTrackSet:{ //可选，如果需要同时播放声音（比如语音通话），需要打开回声消除（并不一定会生效；打开后声音可能会从听筒播放，部分环境下（如小程序、App原生插件）可调用接口切换成扬声器外放）\n\t                //注意：H5、App+renderjs中需要在请求录音权限前进行相同配置RecordApp.RequestPermission_H5OpenSet后此配置才会生效\n\t                echoCancellation:true,noiseSuppression:true,autoGainControl:true} */\n        onProcess: (buffers, powerLevel, duration, sampleRate, newBufferIdx, asyncEnd) => {\n          //全平台通用：可实时上传（发送）数据，配合Recorder.SampleData方法，将buffers中的新数据连续的转换成pcm上传，或使用mock方法将新数据连续的转码成其他格式上传，可以参考Recorder文档里面的：Demo片段列表 -> 实时转码并上传-通用版；基于本功能可以做到：实时转发数据、实时保存数据、实时语音识别（ASR）等\n\n          //注意：App里面是在renderjs中进行实际的音频格式编码操作，此处的buffers数据是renderjs实时转发过来的，修改此处的buffers数据不会改变renderjs中buffers，所以不会改变生成的音频文件，可在onProcess_renderjs中进行修改操作就没有此问题了；如需清理buffers内存，此处和onProcess_renderjs中均需要进行清理，H5、小程序中无此限制\n          //注意：如果你要用只支持在浏览器中使用的Recorder扩展插件，App里面请在renderjs中引入此扩展插件，然后在onProcess_renderjs中调用这个插件；H5可直接在这里进行调用，小程序不支持这类插件；如果调用插件的逻辑比较复杂，建议封装成js文件，这样逻辑层、renderjs中直接import，不需要重复编写\n\n          //H5、小程序等可视化图形绘制，直接运行在逻辑层；App里面需要在onProcess_renderjs中进行这些操作\n          // #ifdef H5 || MP-WEIXIN\n          if (this.waveView) this.waveView.input(buffers[buffers.length - 1], powerLevel, sampleRate)\n          // #endif\n\n          /*实时释放清理内存，用于支持长时间录音；在指定了有效的type时，编码器内部可能还会有其他缓冲，必须同时提供takeoffEncodeChunk才能清理内存，否则type需要提供unknown格式来阻止编码器内部缓冲，App的onProcess_renderjs中需要进行相同操作\n\t                if(this.clearBufferIdx>newBufferIdx){ this.clearBufferIdx=0 } //重新录音了就重置\n\t                for(var i=this.clearBufferIdx||0;i<newBufferIdx;i++) buffers[i]=null;\n\t                this.clearBufferIdx=newBufferIdx; */\n        },\n        onProcess_renderjs: `function(buffers,powerLevel,duration,sampleRate,newBufferIdx,asyncEnd){\n\t                //App中在这里修改buffers会改变生成的音频文件，但注意：buffers会先转发到逻辑层onProcess后才会调用本方法，因此在逻辑层的onProcess中需要重新修改一遍\n\t                //本方法可以返回true，renderjs中的onProcess将开启异步模式，处理完后调用asyncEnd结束异步，注意：这里异步修改的buffers一样的不会在逻辑层的onProcess中生效\n\t                //App中是在renderjs中进行的可视化图形绘制，因此需要写在这里，this是renderjs模块的this（也可以用This变量）；如果代码比较复杂，请直接在renderjs的methods里面放个方法xxxFunc，这里直接使用this.xxxFunc(args)进行调用\n\t                if(this.waveView) this.waveView.input(buffers[buffers.length-1],powerLevel,sampleRate);\n\n\t                /*和onProcess中一样进行释放清理内存，用于支持长时间录音\n\t                if(this.clearBufferIdx>newBufferIdx){ this.clearBufferIdx=0 } //重新录音了就重置\n\t                for(var i=this.clearBufferIdx||0;i<newBufferIdx;i++) buffers[i]=null;\n\t                this.clearBufferIdx=newBufferIdx; */\n\t            }`,\n        onProcessBefore_renderjs: `function(buffers,powerLevel,duration,sampleRate,newBufferIdx){\n\t                //App中本方法会在逻辑层onProcess之前调用，因此修改的buffers会转发给逻辑层onProcess，本方法没有asyncEnd参数不支持异步处理\n\t                //一般无需提供本方法只用onProcess_renderjs就行，renderjs的onProcess内部调用过程：onProcessBefore_renderjs -> 转发给逻辑层onProcess -> onProcess_renderjs\n\t            }`,\n\n        takeoffEncodeChunk: true\n          ? null\n          : (chunkBytes) => {\n              //全平台通用：实时接收到编码器编码出来的音频片段数据，chunkBytes是Uint8Array二进制数据，可以实时上传（发送）出去\n              //App中如果未配置RecordApp.UniWithoutAppRenderjs时，建议提供此回调，因为录音结束后会将整个录音文件从renderjs传回逻辑层，由于uni-app的逻辑层和renderjs层数据交互性能实在太拉跨了，大点的文件传输会比较慢，提供此回调后可避免Stop时产生超大数据回传\n              //App中使用原生插件时，可方便的将数据实时保存到同一文件，第一帧时append:false新建文件，后面的append:true追加到文件\n              //RecordApp.UniNativeUtsPluginCallAsync(\"writeFile\",{path:\"xxx.mp3\",append:回调次数!=1, dataBase64:RecordApp.UniBtoa(chunkBytes.buffer)}).then(...).catch(...)\n            },\n        takeoffEncodeChunk_renderjs: true\n          ? null\n          : `function(chunkBytes){\n\t                //App中这里可以做一些仅在renderjs中才生效的事情，不提供也行，this是renderjs模块的this（也可以用This变量）\n\t            }`,\n\n        start_renderjs: `function(){\n\t                //App中可以放一个函数，在Start成功时renderjs中会先调用这里的代码，this是renderjs模块的this（也可以用This变量）\n\t                //放一些仅在renderjs中才生效的事情，比如初始化，不提供也行\n\t            }`,\n        stop_renderjs: `function(arrayBuffer,duration,mime){\n\t                //App中可以放一个函数，在Stop成功时renderjs中会先调用这里的代码，this是renderjs模块的this（也可以用This变量）\n\t                //放一些仅在renderjs中才生效的事情，不提供也行\n\t            }`\n      }\n\n      RecordApp.UniWebViewActivate(this) //App环境下必须先切换成当前页面WebView\n      RecordApp.Start(\n        set,\n        () => {\n          console.log('已开始录音')\n          //【稳如老狗WDT】可选的，监控是否在正常录音有onProcess回调，如果长时间没有回调就代表录音不正常\n          //var wdt=this.watchDogTimer=setInterval ... 请参考示例Demo的main_recTest.vue中的watchDogTimer实现\n\n          //创建音频可视化图形绘制，App环境下是在renderjs中绘制，H5、小程序等是在逻辑层中绘制，因此需要提供两段相同的代码\n          //view里面放一个canvas，canvas需要指定宽高（下面style里指定了300*100）\n          //<canvas type=\"2d\" class=\"recwave-WaveView\" style=\"width:300px;height:100px\"></canvas>\n          RecordApp.UniFindCanvas(\n            this,\n            ['.recwave-WaveView'],\n            `\n\t                this.waveView=Recorder.WaveView({compatibleCanvas:canvas1, width:300, height:100});\n\t            `,\n            (canvas1) => {\n              this.waveView = Recorder.WaveView({ compatibleCanvas: canvas1, width: 300, height: 100 })\n            }\n          )\n        },\n        (msg) => {\n          console.error('开始录音失败：' + msg)\n        }\n      )\n    },\n\n    //暂停录音\n    recPause() {\n      if (RecordApp.GetCurrentRecOrNull()) {\n        RecordApp.Pause()\n        console.log('已暂停')\n      }\n    },\n    //继续录音\n    recResume() {\n      if (RecordApp.GetCurrentRecOrNull()) {\n        RecordApp.Resume()\n        console.log('继续录音中...')\n      }\n    },\n\n    //停止录音\n    recStop() {\n      //RecordApp.UniNativeUtsPluginCallAsync(\"androidNotifyService\",{ close:true }) //关闭Android App后台录音保活服务\n      this.recordStart = false\n      RecordApp.Stop(\n        (arrayBuffer, duration, mime) => {\n          //全平台通用：arrayBuffer是音频文件二进制数据，可以保存成文件或者发送给服务器\n          //App中如果在Start参数中提供了stop_renderjs，renderjs中的函数会比这个函数先执行\n\n          //注意：当Start时提供了takeoffEncodeChunk后，你需要自行实时保存录音文件数据，因此Stop时返回的arrayBuffer的长度将为0字节\n\n          //如果是H5环境，也可以直接构造成Blob/File文件对象，和Recorder使用一致\n          // #ifdef H5\n          var blob = new Blob([arrayBuffer], { type: mime })\n          console.log(blob, (window.URL || webkitURL).createObjectURL(blob))\n          var file = new File([arrayBuffer], 'recorder.mp3')\n          //uni.uploadFile({file:file, ...}) //参考demo中的test_upload_saveFile.vue\n          console.log('🚀 ~ recStop ~ this.duration:', duration)\n          this.uploadFile({ file: file }, (res) => {\n            console.log('🚀 ~ recStop ~ res:', res)\n            const chatMsg = {\n              content: {\n                msg: `语音 ${Math.round(duration / 1000)}\"`,\n                audioSrc: res\n              },\n              userType: 'self',\n              userId: this.selfUserId,\n              groupId: this.groupId,\n              msgType: 'voice'\n            }\n            this.list.push(chatMsg)\n            this.mqttClient.publish('/chat/server/' + store.state.app.userInfo.userId + '/msg', JSON.stringify(chatMsg))\n            this.$nextTick(() => {\n              this.scrollToBottom()\n            })\n          })\n\n          // #endif\n\n          //如果是App、小程序环境，可以直接保存到本地文件，然后调用相关网络接口上传\n          // #ifdef APP || MP-WEIXIN\n          RecordApp.UniSaveLocalFile(\n            'recorder.mp3',\n            arrayBuffer,\n            (savePath) => {\n              console.log(savePath) //app保存的文件夹为`plus.io.PUBLIC_DOWNLOADS`，小程序为 `wx.env.USER_DATA_PATH` 路径\n              //uni.uploadFile({filePath:savePath, ...}) //参考demo中的test_upload_saveFile.vue\n            },\n            (errMsg) => {\n              console.error(errMsg)\n            }\n          )\n          // #endif\n        },\n        (msg) => {\n          console.error('结束录音失败：' + msg)\n        }\n      )\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n/* ==================== 主容器样式 ==================== */\n.chat-container {\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  background-color: #f5f5f5;\n}\n\n/* ==================== 顶部导航栏 ==================== */\n.chat-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 88rpx;\n  padding: 0 30rpx;\n  background-color: #fff;\n  border-bottom: 1rpx solid #e5e5e5;\n\n  .header-left {\n    width: 80rpx;\n    height: 80rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n\n    .back-icon {\n      font-size: 40rpx;\n      color: #007aff;\n      font-weight: bold;\n    }\n  }\n\n  .header-center {\n    flex: 1;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n\n    .chat-title {\n      font-size: 32rpx;\n      font-weight: 600;\n      color: #333;\n    }\n\n    .online-status {\n      font-size: 24rpx;\n      color: #999;\n      margin-top: 4rpx;\n    }\n  }\n\n  .header-right {\n    width: 80rpx;\n    height: 80rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n\n    .more-icon {\n      font-size: 32rpx;\n      color: #333;\n      font-weight: bold;\n    }\n  }\n}\n\n/* ==================== 消息列表 ==================== */\n.message-list {\n  flex: 1;\n  background-color: #f5f5f5;\n\n  .load-more {\n    display: flex;\n    justify-content: center;\n    padding: 20rpx;\n\n    .loading-text {\n      font-size: 28rpx;\n      color: #999;\n    }\n  }\n\n  .message-content {\n    padding: 20rpx 30rpx 120rpx;\n  }\n\n  .bottom-placeholder {\n    height: 20rpx;\n  }\n}\n\n/* ==================== 时间分隔线 ==================== */\n.time-divider {\n  display: flex;\n  justify-content: center;\n  margin: 30rpx 0;\n\n  .time-text {\n    padding: 8rpx 20rpx;\n    font-size: 24rpx;\n    color: #999;\n    background-color: rgba(0, 0, 0, 0.1);\n    border-radius: 20rpx;\n  }\n}\n\n/* ==================== 消息项 ==================== */\n.message-item {\n  display: flex;\n  margin-bottom: 30rpx;\n\n  &.self-message {\n    flex-direction: row-reverse;\n\n    .message-body {\n      align-items: flex-end;\n    }\n\n    .message-bubble {\n      background-color: #95ec69;\n\n      &::after {\n        right: -12rpx;\n        border-left-color: #95ec69;\n        border-right: none;\n      }\n    }\n  }\n\n  &.friend-message {\n    .message-bubble {\n      background-color: #fff;\n\n      &::after {\n        left: -12rpx;\n        border-right-color: #fff;\n        border-left: none;\n      }\n    }\n  }\n}\n\n/* ==================== 头像容器 ==================== */\n.avatar-container {\n  margin: 0 20rpx;\n\n  .avatar {\n    width: 80rpx;\n    height: 80rpx;\n    border-radius: 8rpx;\n  }\n}\n\n/* ==================== 消息主体 ==================== */\n.message-body {\n  display: flex;\n  flex-direction: column;\n  max-width: 60%;\n\n  .nickname {\n    font-size: 24rpx;\n    color: #999;\n    margin-bottom: 8rpx;\n    padding: 0 20rpx;\n  }\n}\n\n/* ==================== 消息气泡 ==================== */\n.message-bubble {\n  position: relative;\n  padding: 20rpx;\n  border-radius: 12rpx;\n  word-wrap: break-word;\n  word-break: break-all;\n\n  &::after {\n    content: '';\n    position: absolute;\n    top: 20rpx;\n    width: 0;\n    height: 0;\n    border: 12rpx solid transparent;\n  }\n\n  .text-content {\n    font-size: 32rpx;\n    line-height: 1.4;\n    color: #333;\n  }\n\n  .image-content {\n    .message-image {\n      max-width: 400rpx;\n      max-height: 400rpx;\n      border-radius: 8rpx;\n    }\n  }\n\n  .voice-content {\n    display: flex;\n    align-items: center;\n    min-width: 120rpx;\n\n    &.playing {\n      .voice-icon {\n        animation: voice-wave 1s infinite;\n      }\n    }\n\n    .voice-icon {\n      width: 40rpx;\n      height: 40rpx;\n      margin-right: 16rpx;\n\n      &.voice-animation {\n        animation: voice-wave 1s infinite;\n      }\n    }\n\n    .voice-duration {\n      font-size: 28rpx;\n      color: #333;\n    }\n  }\n\n  .message-status {\n    position: absolute;\n    right: -40rpx;\n    top: 50%;\n    transform: translateY(-50%);\n\n    .status-icon {\n      font-size: 24rpx;\n      color: #999;\n\n      &.read {\n        color: #07c160;\n      }\n    }\n  }\n}\n\n/* ==================== 输入工具栏 ==================== */\n.input-toolbar {\n  position: fixed;\n  left: 0;\n  right: 0;\n  background-color: #fff;\n  border-top: 1rpx solid #e5e5e5;\n  padding: 20rpx 30rpx;\n  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));\n  z-index: 1000;\n}\n\n/* ==================== 语音模式 ==================== */\n.voice-mode {\n  .voice-controls {\n    display: flex;\n    align-items: center;\n    gap: 20rpx;\n\n    .mode-switch {\n      width: 60rpx;\n      height: 60rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background-color: #f5f5f5;\n      border-radius: 8rpx;\n      font-size: 28rpx;\n      color: #333;\n    }\n\n    .voice-button {\n      flex: 1;\n      height: 80rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background-color: #f5f5f5;\n      border-radius: 8rpx;\n\n      &.recording {\n        background-color: #ff4757;\n      }\n\n      .voice-text {\n        font-size: 32rpx;\n        color: #333;\n\n        .recording & {\n          color: #fff;\n        }\n      }\n    }\n  }\n}\n\n/* ==================== 文本模式 ==================== */\n.text-mode {\n  .input-controls {\n    display: flex;\n    align-items: flex-end;\n    gap: 20rpx;\n\n    .voice-btn,\n    .emoji-btn,\n    .more-btn {\n      width: 60rpx;\n      height: 60rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 32rpx;\n      color: #666;\n      background-color: #f5f5f5;\n      border-radius: 8rpx;\n    }\n\n    .input-wrapper {\n      flex: 1;\n\n      .text-input {\n        width: 100%;\n        min-height: 60rpx;\n        max-height: 120rpx;\n        padding: 16rpx 20rpx;\n        font-size: 32rpx;\n        line-height: 1.4;\n        background-color: #f5f5f5;\n        border-radius: 8rpx;\n        border: none;\n        outline: none;\n        resize: none;\n      }\n    }\n\n    .send-btn {\n      padding: 16rpx 32rpx;\n      background-color: #07c160;\n      border-radius: 8rpx;\n\n      .send-text {\n        font-size: 28rpx;\n        color: #fff;\n      }\n    }\n  }\n}\n\n/* ==================== 表情面板 ==================== */\n.emoji-panel {\n  position: fixed;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  height: 500rpx;\n  background-color: #fff;\n  border-top: 1rpx solid #e5e5e5;\n  z-index: 999;\n}\n\n/* ==================== 更多功能面板 ==================== */\n.more-panel {\n  position: fixed;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: #fff;\n  border-top: 1rpx solid #e5e5e5;\n  padding: 40rpx 30rpx;\n  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));\n  z-index: 999;\n\n  .more-grid {\n    display: grid;\n    grid-template-columns: repeat(4, 1fr);\n    gap: 40rpx;\n\n    .more-item {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      gap: 16rpx;\n\n      .more-icon {\n        width: 120rpx;\n        height: 120rpx;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-size: 60rpx;\n        background-color: #f5f5f5;\n        border-radius: 20rpx;\n      }\n\n      .more-text {\n        font-size: 24rpx;\n        color: #666;\n      }\n    }\n  }\n}\n\n/* ==================== 录音动画覆盖层 ==================== */\n.recording-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 2000;\n\n  .recording-animation {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    gap: 30rpx;\n    padding: 60rpx;\n    background-color: rgba(0, 0, 0, 0.8);\n    border-radius: 20rpx;\n\n    .wave-container {\n      display: flex;\n      align-items: center;\n      gap: 8rpx;\n\n      .wave {\n        width: 8rpx;\n        height: 40rpx;\n        background-color: #07c160;\n        border-radius: 4rpx;\n        animation: wave-animation 1s ease-in-out infinite alternate;\n      }\n    }\n\n    .recording-text {\n      font-size: 32rpx;\n      color: #fff;\n      font-weight: 600;\n    }\n\n    .recording-tip {\n      font-size: 24rpx;\n      color: rgba(255, 255, 255, 0.8);\n    }\n  }\n}\n\n/* ==================== 动画定义 ==================== */\n@keyframes voice-wave {\n  0%,\n  100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.2);\n  }\n}\n\n@keyframes wave-animation {\n  0% {\n    height: 20rpx;\n  }\n  100% {\n    height: 80rpx;\n  }\n}\n\n/* ==================== 兼容旧版本样式 ==================== */\n.message {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 30rpx;\n\n  .avatar {\n    width: 80rpx;\n    height: 80rpx;\n    border-radius: 10rpx;\n    margin-right: 30rpx;\n    margin-top: 20rpx;\n  }\n\n  .content {\n    min-height: 80rpx;\n    max-width: 60vw;\n    box-sizing: border-box;\n    font-size: 28rpx;\n    line-height: 1.3;\n    padding: 20rpx;\n    border-radius: 10rpx;\n    background: #fff;\n\n    image {\n      width: 200rpx;\n      height: 200rpx;\n    }\n  }\n\n  &.self {\n    justify-content: flex-end;\n\n    .avatar {\n      margin: 0 0 0 30rpx;\n    }\n    .msg-box {\n      display: flex;\n      align-items: flex-start;\n    }\n\n    .content {\n      position: relative;\n\n      &::after {\n        position: absolute;\n        content: '';\n        width: 0;\n        height: 0;\n        border: 16rpx solid transparent;\n        border-left: 16rpx solid #fff;\n        right: -28rpx;\n        top: 24rpx;\n      }\n    }\n  }\n\n  &.friend {\n    .content {\n      position: relative;\n      word-wrap: break-word;\n      word-break: break-all;\n\n      &::after {\n        position: absolute;\n        content: '';\n        width: 0;\n        height: 0;\n        border: 16rpx solid transparent;\n        border-right: 16rpx solid #fff;\n        left: -28rpx;\n        top: 24rpx;\n      }\n    }\n  }\n}\n\n.tool {\n  position: fixed;\n  width: 100%;\n  min-height: 120rpx;\n  left: 0;\n  bottom: 0;\n  background: #fff;\n  display: flex;\n  // align-items: flex-start;\n  align-items: center;\n  box-sizing: border-box;\n  padding: 20rpx 24rpx 20rpx 24rpx;\n  padding-bottom: calc(20rpx + constant(safe-area-inset-bottom) / 2) !important;\n  padding-bottom: calc(20rpx + env(safe-area-inset-bottom) / 2) !important;\n\n  .left-icon {\n    width: 56rpx;\n    height: 56rpx;\n    margin-right: 10rpx;\n  }\n\n  .input,\n  .voice-crl {\n    background: #eee;\n    border-radius: 10rpx;\n    height: 70rpx;\n    margin-right: 16rpx;\n    flex: 1;\n    padding: 0 20rpx;\n    box-sizing: border-box;\n    font-size: 28rpx;\n  }\n\n  .thumb {\n    width: 64rpx;\n    height: 64rpx;\n  }\n\n  .voice-crl {\n    text-align: center;\n    line-height: 70rpx;\n    font-weight: bold;\n  }\n}\n.send-btn {\n  width: 0;\n  height: 56rpx;\n  background-color: #00c75a;\n  color: #fff;\n  padding: 0;\n  font-size: 28rpx;\n  border-radius: 12rpx;\n  line-height: 56rpx;\n  margin-left: 12rpx;\n  transition: all 0.3s ease-in;\n}\n.show-send-btn {\n  width: 100rpx !important;\n}\n\n.audio-animation {\n  position: fixed;\n  // width: 100vw;\n  // height: 100vh;\n  left: 50%;\n  top: 50%;\n  transform: translate(-50%, -50%);\n  z-index: 202410;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n\n  .text {\n    text-align: center;\n    font-size: 28rpx;\n    color: #333;\n    margin-top: 60rpx;\n  }\n\n  .audio-wave {\n    padding: 50rpx;\n\n    .audio-wave-text {\n      background-color: blue;\n      width: 7rpx;\n      height: 12rpx;\n      margin: 0 6rpx;\n      border-radius: 5rpx;\n      display: inline-block;\n      border: none;\n      animation: wave 0.25s ease-in-out;\n      animation-iteration-count: infinite;\n      animation-direction: alternate;\n    }\n\n    /*  声波动画  */\n    @keyframes wave {\n      from {\n        transform: scaleY(1);\n      }\n\n      to {\n        transform: scaleY(4);\n      }\n    }\n  }\n}\n.nickname {\n  font-size: 24rpx;\n  margin-bottom: 6rpx;\n  color: #a6a6a6;\n}\n.right-name {\n  display: flex;\n  align-items: center;\n}\n.time {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 12rpx;\n  .time-text {\n    padding: 10rpx;\n    font-size: 24rpx;\n    color: #333;\n    background-color: rgba(255, 255, 255, 0.5);\n    border-radius: 8rpx;\n  }\n}\n.icon-voice {\n  width: 30rpx !important;\n  height: 30rpx;\n  margin-left: 12rpx;\n}\n.rate-90 {\n  transform: rotate(180deg);\n  margin-right: 12rpx;\n}\n.item-center {\n  display: flex;\n  align-items: center;\n}\n</style>\n", "import mod from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=style&index=0&id=0b926c58&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=style&index=0&id=0b926c58&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754987004054\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}