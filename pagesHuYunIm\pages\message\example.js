/**
 * 优化后的消息页面使用示例
 * 展示如何正确使用新的消息页面组件
 */

// ==================== 基本使用示例 ====================

/**
 * 从聊天列表跳转到消息页面
 */
function navigateToChat() {
  const chatInfo = {
    groupId: '1911787038405492737',
    name: '技术交流群',
    avatar: 'https://example.com/avatar.jpg'
  }
  
  uni.navigateTo({
    url: `/pagesHuYunIm/pages/message/message?groupId=${chatInfo.groupId}&name=${encodeURIComponent(chatInfo.name)}&avatar=${encodeURIComponent(chatInfo.avatar)}`
  })
}

/**
 * 从用户资料页面发起私聊
 */
function startPrivateChat(userId, nickname, avatar) {
  // 创建私聊群组（通常由后端API处理）
  createPrivateChatGroup(userId).then(groupId => {
    uni.navigateTo({
      url: `/pagesHuYunIm/pages/message/message?groupId=${groupId}&name=${encodeURIComponent(nickname)}&avatar=${encodeURIComponent(avatar)}`
    })
  })
}

// ==================== MQTT配置示例 ====================

/**
 * 在app.js中初始化MQTT配置
 */
function initMqttInApp() {
  // 导入MQTT工具包
  import mqttClient from '@/utils/mqttClient.js'
  import { createUserInfo } from '@/utils/mqttConfig.js'
  import mqtt from '@/utils/mqtt.min.js'
  
  // 在应用启动时设置MQTT库
  mqttClient.setMqttLib(mqtt)
  
  // 可以在这里设置全局的MQTT事件监听
  const globalCallbacks = {
    onConnect: () => {
      console.log('全局MQTT连接成功')
      // 可以在这里处理全局连接成功逻辑
    },
    onError: (error) => {
      console.error('全局MQTT连接错误:', error)
      // 可以在这里处理全局错误，比如显示重连提示
    }
  }
}

// ==================== 消息处理示例 ====================

/**
 * 自定义消息处理器
 */
class MessageHandler {
  constructor() {
    this.messageQueue = []
    this.isProcessing = false
  }
  
  /**
   * 处理接收到的消息
   */
  handleIncomingMessage(mqttMsg) {
    switch(mqttMsg.command) {
      case 'chatMsg':
        this.handleChatMessage(mqttMsg.data)
        break
      case 'withdraw':
        this.handleWithdrawMessage(mqttMsg.data)
        break
      case 'forbidden':
        this.handleForbiddenMessage(mqttMsg.data)
        break
      case 'kickOut':
        this.handleKickOutMessage(mqttMsg.data)
        break
      default:
        console.log('未知消息类型:', mqttMsg.command)
    }
  }
  
  /**
   * 处理聊天消息
   */
  handleChatMessage(chatMsg) {
    // 添加到消息队列
    this.messageQueue.push(chatMsg)
    
    // 如果当前页面是对应的聊天页面，直接显示
    const currentPages = getCurrentPages()
    const currentPage = currentPages[currentPages.length - 1]
    
    if (currentPage.route.includes('message') && 
        currentPage.options.groupId === chatMsg.groupId) {
      // 当前就在聊天页面，直接添加消息
      currentPage.$vm.list.push(chatMsg)
      currentPage.$vm.scrollToBottom()
    } else {
      // 不在聊天页面，显示通知
      this.showMessageNotification(chatMsg)
    }
  }
  
  /**
   * 显示消息通知
   */
  showMessageNotification(chatMsg) {
    // 可以使用uni-app的本地通知
    uni.showToast({
      title: `${chatMsg.nickname}: ${chatMsg.content}`,
      icon: 'none',
      duration: 3000
    })
    
    // 或者使用自定义通知组件
    // this.showCustomNotification(chatMsg)
  }
  
  /**
   * 处理消息撤回
   */
  handleWithdrawMessage(withdrawData) {
    console.log('消息被撤回:', withdrawData)
    // 在当前聊天页面中移除对应消息
    const currentPages = getCurrentPages()
    const currentPage = currentPages[currentPages.length - 1]
    
    if (currentPage.route.includes('message') && 
        currentPage.options.groupId === withdrawData.groupId) {
      const messageList = currentPage.$vm.list
      const index = messageList.findIndex(msg => msg.id === withdrawData.id)
      if (index !== -1) {
        messageList[index].content = '消息已被撤回'
        messageList[index].msgType = 'withdraw'
      }
    }
  }
}

// ==================== 文件上传示例 ====================

/**
 * 图片上传处理
 */
function uploadImageExample(filePath) {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: '/jeecg-boot/huyun/front/chat/upload',
      filePath: filePath,
      name: 'file',
      header: {
        'x-access-token': getToken() // 获取用户token
      },
      success: (res) => {
        if (res.statusCode === 200) {
          const data = JSON.parse(res.data)
          resolve(data.message) // 返回图片URL
        } else {
          reject(new Error('上传失败'))
        }
      },
      fail: (error) => {
        reject(error)
      }
    })
  })
}

/**
 * 语音上传处理
 */
function uploadVoiceExample(audioFile, duration) {
  return new Promise((resolve, reject) => {
    // 如果是H5环境，使用File对象
    if (typeof audioFile === 'object' && audioFile instanceof File) {
      uni.uploadFile({
        file: audioFile,
        url: '/jeecg-boot/huyun/front/chat/upload',
        name: 'file',
        header: {
          'x-access-token': getToken()
        },
        success: (res) => {
          if (res.statusCode === 200) {
            const data = JSON.parse(res.data)
            resolve({
              msg: `语音 ${Math.round(duration / 1000)}"`,
              audioSrc: data.message
            })
          } else {
            reject(new Error('上传失败'))
          }
        },
        fail: reject
      })
    } else {
      // 如果是小程序或App环境，使用文件路径
      uni.uploadFile({
        url: '/jeecg-boot/huyun/front/chat/upload',
        filePath: audioFile,
        name: 'file',
        header: {
          'x-access-token': getToken()
        },
        success: (res) => {
          if (res.statusCode === 200) {
            const data = JSON.parse(res.data)
            resolve({
              msg: `语音 ${Math.round(duration / 1000)}"`,
              audioSrc: data.message
            })
          } else {
            reject(new Error('上传失败'))
          }
        },
        fail: reject
      })
    }
  })
}

// ==================== 工具函数示例 ====================

/**
 * 获取用户token
 */
function getToken() {
  // 从store或缓存中获取token
  return uni.getStorageSync('token') || ''
}

/**
 * 创建私聊群组
 */
function createPrivateChatGroup(userId) {
  return new Promise((resolve, reject) => {
    uni.request({
      url: '/api/chat/createPrivateGroup',
      method: 'POST',
      data: { userId },
      header: {
        'x-access-token': getToken()
      },
      success: (res) => {
        if (res.data.success) {
          resolve(res.data.result.groupId)
        } else {
          reject(new Error(res.data.message))
        }
      },
      fail: reject
    })
  })
}

/**
 * 格式化消息时间
 */
function formatMessageTime(timestamp) {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  // 今天
  if (diff < 24 * 60 * 60 * 1000 && now.getDate() === date.getDate()) {
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }
  
  // 昨天
  const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)
  if (yesterday.getDate() === date.getDate()) {
    return '昨天 ' + date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }
  
  // 更早
  return date.toLocaleDateString('zh-CN') + ' ' + 
         date.toLocaleTimeString('zh-CN', { 
           hour: '2-digit', 
           minute: '2-digit' 
         })
}

// ==================== 导出示例 ====================

export {
  navigateToChat,
  startPrivateChat,
  MessageHandler,
  uploadImageExample,
  uploadVoiceExample,
  formatMessageTime
}
