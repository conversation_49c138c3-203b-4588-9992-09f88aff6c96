<view class="chat-container data-v-0b926c58"><view class="chat-header data-v-0b926c58"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="header-left data-v-0b926c58" bindtap="__e"><text class="back-icon data-v-0b926c58">‹</text></view><view class="header-center data-v-0b926c58"><text class="chat-title data-v-0b926c58">{{chatTitle}}</text><block wx:if="{{isOnline}}"><text class="online-status data-v-0b926c58">在线</text></block></view><view data-event-opts="{{[['tap',[['showChatMenu',['$event']]]]]}}" class="header-right data-v-0b926c58" bindtap="__e"><text class="more-icon data-v-0b926c58">⋯</text></view></view><scroll-view class="message-list data-v-0b926c58" scroll-y="{{true}}" scroll-with-animation="{{true}}" scroll-top="{{scrollTop}}" enable-back-to-top="{{true}}" data-event-opts="{{[['scrolltoupper',[['loadMoreMessages',['$event']]]]]}}" bindscrolltoupper="__e"><block wx:if="{{loading}}"><view class="load-more data-v-0b926c58"><text class="loading-text data-v-0b926c58">加载中...</text></view></block><view class="message-content data-v-0b926c58"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index"><block class="data-v-0b926c58"><block wx:if="{{item.$orig.showCreateTime}}"><view class="time-divider data-v-0b926c58"><text class="time-text data-v-0b926c58">{{item.m0}}</text></view></block><view class="{{item.m1}}"><block wx:if="{{!item.m2}}"><view class="avatar-container data-v-0b926c58"><image class="avatar data-v-0b926c58" src="{{item.m3}}" mode="aspectFill" data-event-opts="{{[['tap',[['showUserProfile',['$0'],[[['messageList','id||index',item.$orig.id||index,'userId']]]]]]]}}" bindtap="__e"></image></view></block><view class="message-body data-v-0b926c58"><block wx:if="{{item.m4}}"><view class="nickname data-v-0b926c58">{{''+item.m5+''}}</view></block><view data-event-opts="{{[['tap',[['handleMessageClick',['$0'],[[['messageList','id||index',item.$orig.id||index]]]]]],['longpress',[['showMessageMenu',['$0'],[[['messageList','id||index',item.$orig.id||index]]]]]]]}}" class="message-bubble data-v-0b926c58" bindtap="__e" bindlongpress="__e"><block wx:if="{{item.$orig.msgType==='text'}}"><view class="text-content data-v-0b926c58">{{''+item.$orig.content+''}}</view></block><block wx:else><block wx:if="{{item.$orig.msgType==='image'}}"><view class="image-content data-v-0b926c58"><image class="message-image data-v-0b926c58" src="{{item.$orig.content}}" mode="aspectFill" data-event-opts="{{[['tap',[['previewImage',['$0'],[[['messageList','id||index',item.$orig.id||index,'content']]]]]]]}}" bindtap="__e"></image></view></block><block wx:else><block wx:if="{{item.$orig.msgType==='voice'}}"><view class="{{['voice-content','data-v-0b926c58',(item.$orig.isPlaying)?'playing':'']}}"><image class="{{['voice-icon','data-v-0b926c58',(item.$orig.isPlaying)?'voice-animation':'']}}" src="{{item.m6}}"></image><text class="voice-duration data-v-0b926c58">{{item.m7}}</text></view></block></block></block><block wx:if="{{item.m8}}"><view class="message-status data-v-0b926c58"><block wx:if="{{item.$orig.status==='sending'}}"><text class="status-icon data-v-0b926c58">⏳</text></block><block wx:else><block wx:if="{{item.$orig.status==='failed'}}"><text class="status-icon data-v-0b926c58">❌</text></block><block wx:else><block wx:if="{{item.$orig.status==='sent'}}"><text class="status-icon data-v-0b926c58">✓</text></block><block wx:else><block wx:if="{{item.$orig.status==='read'}}"><text class="status-icon read data-v-0b926c58">✓✓</text></block></block></block></block></view></block></view></view><block wx:if="{{item.m9}}"><view class="avatar-container data-v-0b926c58"><image class="avatar data-v-0b926c58" src="{{selfAvatar}}" mode="aspectFill"></image></view></block></view></block></block></view><view class="bottom-placeholder data-v-0b926c58"></view></scroll-view><view class="input-toolbar data-v-0b926c58" style="{{'bottom:'+(keyboardHeight+'px')+';'}}"><block wx:if="{{inputMode==='voice'}}"><view class="voice-mode data-v-0b926c58"><view class="voice-controls data-v-0b926c58"><text data-event-opts="{{[['tap',[['switchToTextMode',['$event']]]]]}}" class="mode-switch data-v-0b926c58" bindtap="__e">文</text><view data-event-opts="{{[['touchstart',[['startRecording',['$event']]]],['touchend',[['stopRecording',['$event']]]],['touchcancel',[['cancelRecording',['$event']]]]]}}" class="{{['voice-button','data-v-0b926c58',(isRecording)?'recording':'']}}" bindtouchstart="__e" bindtouchend="__e" bindtouchcancel="__e"><text class="voice-text data-v-0b926c58">{{voiceButtonText}}</text></view></view></view></block><block wx:else><view class="text-mode data-v-0b926c58"><view class="input-controls data-v-0b926c58"><text data-event-opts="{{[['tap',[['switchToVoiceMode',['$event']]]]]}}" class="voice-btn data-v-0b926c58" bindtap="__e">🎤</text><view class="input-wrapper data-v-0b926c58"><textarea class="text-input data-v-0b926c58" placeholder="输入消息..." focus="{{inputFocus}}" auto-height="{{true}}" max-height="{{120}}" data-event-opts="{{[['focus',[['handleInputFocus',['$event']]]],['blur',[['handleInputBlur',['$event']]]],['input',[['__set_model',['','inputText','$event',[]]],['handleInput',['$event']]]],['confirm',[['sendTextMessage',['$event']]]]]}}" value="{{inputText}}" bindfocus="__e" bindblur="__e" bindinput="__e" bindconfirm="__e"></textarea></view><text data-event-opts="{{[['tap',[['toggleEmojiPanel',['$event']]]]]}}" class="emoji-btn data-v-0b926c58" bindtap="__e">😊</text><block wx:if="{{!$root.g0}}"><text data-event-opts="{{[['tap',[['toggleMorePanel',['$event']]]]]}}" class="more-btn data-v-0b926c58" bindtap="__e">+</text></block><block wx:if="{{$root.g1}}"><view data-event-opts="{{[['tap',[['sendTextMessage',['$event']]]]]}}" class="send-btn data-v-0b926c58" bindtap="__e"><text class="send-text data-v-0b926c58">发送</text></view></block></view></view></block></view><block wx:if="{{showEmojiPanel}}"><view class="emoji-panel data-v-0b926c58"></view></block><block wx:if="{{showMorePanel}}"><view class="more-panel data-v-0b926c58"><view class="more-grid data-v-0b926c58"><view data-event-opts="{{[['tap',[['chooseImage',['$event']]]]]}}" class="more-item data-v-0b926c58" bindtap="__e"><text class="more-icon data-v-0b926c58">📷</text><text class="more-text data-v-0b926c58">相册</text></view><view data-event-opts="{{[['tap',[['takePhoto',['$event']]]]]}}" class="more-item data-v-0b926c58" bindtap="__e"><text class="more-icon data-v-0b926c58">📸</text><text class="more-text data-v-0b926c58">拍照</text></view><view data-event-opts="{{[['tap',[['chooseVideo',['$event']]]]]}}" class="more-item data-v-0b926c58" bindtap="__e"><text class="more-icon data-v-0b926c58">🎬</text><text class="more-text data-v-0b926c58">视频</text></view><view data-event-opts="{{[['tap',[['chooseFile',['$event']]]]]}}" class="more-item data-v-0b926c58" bindtap="__e"><text class="more-icon data-v-0b926c58">📁</text><text class="more-text data-v-0b926c58">文件</text></view></view></view></block><block wx:if="{{isRecording}}"><view class="recording-overlay data-v-0b926c58"><view class="recording-animation data-v-0b926c58"><view class="wave-container data-v-0b926c58"><block wx:for="{{5}}" wx:for-item="i" wx:for-index="__i0__" wx:key="*this"><view class="wave data-v-0b926c58" style="{{'animation-delay:'+(i*0.1+'s')+';'}}"></view></block></view><text class="recording-text data-v-0b926c58">{{recordingText}}</text><text class="recording-tip data-v-0b926c58">松开发送，上滑取消</text></view></view></block></view>