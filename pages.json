{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "uni-app"
			}
		}
	],
	"subPackages": [{
			"root": "pagesHuYunIm",
			"pages": [{
					"path": "pages/index/index",
					"style": {
						"navigationBarTitleText": "微信",
						"navigationBarBackgroundColor": "#ffffff",
						"navigationBarTextStyle": "black",
						"enablePullDownRefresh": true,
						"backgroundTextStyle": "dark"
					}
				},
				{
					"path": "pages/chat/index",
					"style": {
						"navigationBarTitleText": "",
						"navigationStyle": "custom",
						"app-plus": {
							"softinputNavBar": "none",
							"bounce": "none"
						}
					}
				}
				// {
				// 	"path": "pages/message/message",
				// 	"style": {
				// 		"navigationBarTitleText": "消息"
				// 	}
				// }
			]
		},
		{
			"root": "pagesGoEasy",
			"pages": [{
					"path": "chat_page/index",
					"style": {
						"navigationStyle": "custom",
						"app-plus": {
							"softinputNavBar": "none",
							"bounce": "none"
						}
					}
				},
				{
					"path": "sessionList/index",
					"style": {
						"navigationBarTitleText": "会话",
						"navigationBarBackgroundColor": "#ffffff",
						"navigationBarTextStyle": "black",
						"navigationStyle": "custom",
						"app-plus": {
							"titleNView": true
						}
					}
				},
				{
					"path": "group_infor/index",
					"style": {
						"navigationBarTitleText": "聊天信息"
					}
				},
				{
					"path": "group_set_font_size/index",
					"style": {
						"navigationStyle": "custom",
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "group_member_infor/index",
					"style": {
						"navigationBarBackgroundColor": "#fff",
						"navigationBarTitleText": "信息",
						"app-plus": {
							"bounce": "none"
						}
					}
				},
				{
					"path": "group_code/index",
					"style": {
						"navigationBarTitleText": "扫码进群"
					}
				},
				{
					"path": "group_report/index",
					"style": {
						"navigationBarTitleText": "投诉群聊"
					}
				},
				{
					"path": "group_announcement_add/index",
					"style": {
						"navigationBarTitleText": "群公告",
						"app-plus": {
							"softinputNavBar": "none",
							"bounce": "none"
						}
					}
				},
				{
					"path": "group_name_edit/index",
					"style": {
						"navigationBarBackgroundColor": "#fff",
						"navigationBarTitleText": "",
						"app-plus": {
							"softinputNavBar": "none",
							"bounce": "none"
						}
					}
				},
				{
					"path": "group_nickname_edit/index",
					"style": {
						"navigationBarBackgroundColor": "#fff",
						"navigationBarTitleText": "",
						"app-plus": {
							"softinputNavBar": "none",
							"bounce": "none"
						}
					}
				},
				{
					"path": "group_code_enter/index",
					"style": {
						"navigationBarTitleText": "群聊"
					}
				},
				{
					"path": "envelope_sending/index",
					"style": {
						"navigationBarTitleText": "发红包",
						"navigationBarBackgroundColor": "#ededed",
						"app-plus": {
							"softinputNavBar": "none",
							"bounce": "none"
						}
					}
				},
				{
					"path": "envelope_receive/index",
					"style": {
						"navigationBarTitleText": "红包详情",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "group_create/index",
					"style": {
						"navigationBarTitleText": "创建群聊",
						"navigationStyle": "custom"
					}
				},
				{
					"path": "group_history_get/index",
					"style": {
						"navigationBarTitleText": "按日期查找"
					}
				},
				{
					"path": "admin/addressBook",
					"style": {
						"navigationBarTitleText": "通讯录",
						"navigationBarBackgroundColor": "#ececec"
					}
				},
				{
					"path": "admin/create_group",
					"style": {
						"navigationBarTitleText": "建群",
						"navigationBarBackgroundColor": "#ececec"
					}
				}
			]
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8"
	},
	"uniIdRouter": {}
}