{"version": 3, "sources": ["webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/bottom-operation/m-recorder.vue?549b", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/bottom-operation/m-recorder.vue?2d4e", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/bottom-operation/m-recorder.vue?12f8", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/bottom-operation/m-recorder.vue?bd60", "uni-app:///pagesHuYunIm/pages/chat/components/bottom-operation/m-recorder.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/bottom-operation/m-recorder.vue?4bcb", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/bottom-operation/m-recorder.vue?5f75"], "names": ["props", "value", "type", "default", "isCancel", "data", "recordTime", "isShow", "emojiList", "watch", "handler", "recordTimeInterval", "clearInterval", "view", "immediate", "methods", "touchend"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACiM;AACjM,gBAAgB,yLAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,mBAAO,CAAC,iDAAwC;AACxD;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpBA;AAAA;AAAA;AAAA;AAA6vB,CAAgB,grBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC+BjxB;AAAA,eACA;EACAA;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;EACA;EACAE;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAR;MACAS;QAAA;QACA;QACA;QACA;UACAC;YACA;YACA;cACA;YACA;UACA;QACA;UACAC;QACA;QACA;UACA;UACAC;YACA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;MACA;;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3FA;AAAA;AAAA;AAAA;AAAw7C,CAAgB,uuCAAG,EAAC,C;;;;;;;;;;;ACA58C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesHuYunIm/pages/chat/components/bottom-operation/m-recorder.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./m-recorder.vue?vue&type=template&id=33685ec2&scoped=true&\"\nvar renderjs\nimport script from \"./m-recorder.vue?vue&type=script&lang=js&\"\nexport * from \"./m-recorder.vue?vue&type=script&lang=js&\"\nimport style0 from \"./m-recorder.vue?vue&type=style&index=0&id=33685ec2&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"33685ec2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesHuYunIm/pages/chat/components/bottom-operation/m-recorder.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-recorder.vue?vue&type=template&id=33685ec2&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    _vm.recordTime < 50\n      ? require(\"../../../../static/chat_page/sound.gif\")\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-recorder.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-recorder.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"m-recorder\" v-show=\"isShow\">\n\t\t<view class=\"icon_ m-recorder-gif\">\n\t\t\t<image class=\"img\" :src=\"require('../../../../static/chat_page/sound.gif')\" mode=\"heightFix\" v-if=\"recordTime < 50\"></image>\n\t\t\t<view class=\"color_4a\" v-else>{{ 60 - recordTime }}\"后将停止录音</view>\n\t\t</view>\n\t\t<view class=\"m-recorder-str2\" :class=\"{ m_recorder_str2: isCancel }\">\n\t\t\t<view class=\"icon_ size_white m-recorder-text\" v-if=\"isCancel\">\n\t\t\t\t松手\n\t\t\t\t<view style=\"width: 10rpx\"></view>\n\t\t\t\t取消\n\t\t\t</view>\n\t\t\t<view class=\"icon_ size_white m-recorder-text\" v-else>\n\t\t\t\t上划\n\t\t\t\t<view style=\"width: 10rpx\"></view>\n\t\t\t\t取消\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"m-recorder-str\">\n\t\t\t<view class=\"m-recorder-str-icon\">\n\t\t\t\t<image\n\t\t\t\t\tclass=\"img\"\n\t\t\t\t\tsrc=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTY3NC44MDYgNzcuMTI2YTQ2LjAwOCA0Ni4wMDggMCAwIDAtNjYuMTc2IDAgNDYuMDA4IDQ2LjAwOCAwIDAgMCAwIDYzLjAyNSA1MjAuNTg4IDUyMC41ODggMCAwIDEgMCA3MzUuNTA0IDQ3LjI2OSA0Ny4yNjkgMCAwIDAgMCA2Ni44MDcgNDcuMjY5IDQ3LjI2OSAwIDAgMCA2Ni44MDcgMCA2MTQuNDk2IDYxNC40OTYgMCAwIDAtLjYzLTg2NS4zMzZ6IiBmaWxsPSIjNTE1MTUxIi8+PHBhdGggZD0iTTQ2Ny40NTMgMjQyLjg4MmE0Ny4yNjkgNDcuMjY5IDAgMCAwLTYzLjAyNSAwIDQ2LjYzOSA0Ni42MzkgMCAwIDAgMCA2My4wMjYgMjk5LjM3IDI5OS4zNyAwIDAgMSAwIDQyMi4yNjggNDcuMjY5IDQ3LjI2OSAwIDAgMCAwIDYzLjAyNiA0Ni42MzkgNDYuNjM5IDAgMCAwIDYzLjAyNSAwIDM5My45MDggMzkzLjkwOCAwIDAgMCAwLTU0OC4zMnptLTI3Ny4zMSAyMTQuOTE2YTc4LjE1MSA3OC4xNTEgMCAwIDAgMCAxMTAuOTI1IDc4Ljc4MiA3OC43ODIgMCAxIDAgMC0xMTAuOTI1eiIgZmlsbD0iIzUxNTE1MSIvPjwvc3ZnPg==\"\n\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t></image>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nlet recordTimeInterval = null;\nexport default {\n\tprops: {\n\t\tvalue: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\tisCancel: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t}\n\t},\n\tdata() {\n\t\treturn {\n\t\t\trecordTime: 1,\n\t\t\tisShow: false,\n\t\t\temojiList: []\n\t\t};\n\t},\n\twatch: {\n\t\tvalue: {\n\t\t\thandler: function (newV) {\n\t\t\t\tthis.isShow = newV;\n\t\t\t\tthis.recordTime = 1;\n\t\t\t\tif (newV) {\n\t\t\t\t\trecordTimeInterval = setInterval(() => {\n\t\t\t\t\t\tthis.recordTime++;\n\t\t\t\t\t\tif (this.recordTime === 60) {\n\t\t\t\t\t\t\tthis.touchend();\n\t\t\t\t\t\t}\n\t\t\t\t\t}, 1000);\n\t\t\t\t} else {\n\t\t\t\t\tclearInterval(recordTimeInterval);\n\t\t\t\t}\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\tlet view = uni.createSelectorQuery().select('.m-recorder-str');\n\t\t\t\t\tview.boundingClientRect((data) => {\n\t\t\t\t\t\tthis.$emit('recorderTop', data);\n\t\t\t\t\t}).exec();\n\n\t\t\t\t\t// setTimeout(() => {\n\t\t\t\t\t// \tconst query = uni.createSelectorQuery().in(this);\n\t\t\t\t\t// \tquery\n\t\t\t\t\t// \t\t.select('.m-recorder-str')\n\t\t\t\t\t// \t\t.boundingClientRect((data) => {\n\t\t\t\t\t// \t\t\tconsole.log('得到布局位置信息' + JSON.stringify(data));\n\t\t\t\t\t// \t\t\tconsole.log('节点离页面顶部的距离为' + data.top);\n\t\t\t\t\t// \t\t})\n\t\t\t\t\t// \t\t.exec();\n\t\t\t\t\t// }, 1000);\n\t\t\t\t});\n\t\t\t},\n\t\t\timmediate: true\n\t\t}\n\t},\n\tmethods: {\n\t\ttouchend() {\n\t\t\tthis.$emit('touchend');\n\t\t}\n\t}\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.m-recorder {\n\tposition: fixed;\n\tz-index: 9999;\n\ttop: 0;\n\tleft: 0;\n\tbottom: 0;\n\tright: 0;\n\toverflow: hidden;\n\tbackground-color: rgba(0, 0, 0, 0.75);\n\n\t.m-recorder-gif {\n\t\tposition: absolute;\n\t\ttop: 50vh;\n\t\tleft: calc(50% - 160rpx);\n\t\tz-index: 99;\n\t\twidth: 340rpx;\n\t\theight: 170rpx;\n\t\tbox-sizing: border-box;\n\t\tpadding: 16rpx;\n\t\tborder-radius: 30rpx;\n\t\tbackground-color: #95ec6a;\n\t\t.img {\n\t\t\twidth: 100%;\n\t\t\theight: 100%;\n\t\t}\n\t}\n\t.m-recorder-gif::after {\n\t\tposition: absolute;\n\t\tz-index: -1;\n\t\tcontent: '';\n\t\tbottom: -10rpx;\n\t\tleft: calc(50% - 18rpx);\n\t\twidth: 36rpx;\n\t\theight: 36rpx;\n\t\tborder-radius: 2px;\n\t\ttransform: rotate(45deg);\n\t\tbackground-color: #95ec6a;\n\t}\n\n\t.m-recorder-str {\n\t\tbox-sizing: border-box;\n\t\tposition: absolute;\n\t\ttop: calc(100vh - 300rpx);\n\t\tleft: calc(50% - 150vw);\n\t\twidth: 300vw;\n\t\theight: 300vw;\n\t\tborder: 12rpx solid #cccccc;\n\t\tborder-radius: 50%;\n\t\tbackground-image: radial-gradient(#fff, #dddddd, #797979);\n\t\t.m-recorder-str-icon {\n\t\t\tposition: absolute;\n\t\t\ttop: 100rpx;\n\t\t\tleft: calc(50% - 25rpx);\n\t\t\twidth: 60rpx;\n\t\t\theight: 60rpx;\n\t\t}\n\t}\n\t.m-recorder-str2 {\n\t\tbox-sizing: border-box;\n\t\tposition: absolute;\n\t\ttop: calc(100vh - 400rpx);\n\t\tleft: calc(50% - 150vw);\n\t\twidth: 300vw;\n\t\theight: 300vw;\n\t\topacity: 0.7;\n\t\tbackground-color: rgba(240, 240, 240, 0.3);\n\t\tborder-radius: 50%;\n\t\ttransition: all 0.2s;\n\t\t.m-recorder-text {\n\t\t\twidth: 200rpx;\n\t\t\tposition: absolute;\n\t\t\ttop: 30rpx;\n\t\t\tleft: calc(50% - 100rpx);\n\t\t}\n\t}\n\t.m_recorder_str2 {\n\t\tbackground-color: #fa5251;\n\t\tbox-shadow: #fa5251 0px 0px 50px;\n\t}\n}\n</style>\n", "import mod from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-recorder.vue?vue&type=style&index=0&id=33685ec2&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-recorder.vue?vue&type=style&index=0&id=33685ec2&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754983153789\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}