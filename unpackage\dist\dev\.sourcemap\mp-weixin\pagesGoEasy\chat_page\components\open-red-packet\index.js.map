{"version": 3, "sources": ["webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/components/open-red-packet/index.vue?d4ff", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/components/open-red-packet/index.vue?53f1", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/components/open-red-packet/index.vue?4d78", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/components/open-red-packet/index.vue?5f9e", "uni-app:///pagesGoEasy/chat_page/components/open-red-packet/index.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/components/open-red-packet/index.vue?dd33", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/components/open-red-packet/index.vue?09a3"], "names": ["components", "data", "myid", "isOpen", "pageObj", "senderData", "item", "payload", "methods", "onClose", "open", "uni", "title", "console", "自己的信息", "type", "to_uid", "red_money", "remark", "is_refund", "had_draw", "is_end", "my_red", "red_packet_bg", "onOpen", "messageId", "group_id", "open_red_packet", "code", "envelopeClickList", "key", "AudioContext", "setTimeout", "first"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC8L;AAC9L,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAyuB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC0G7vB;AAIA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AAAA,eACA;EACAA,aAIA;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;MACA;MACAC;QACAD;QACAE;MACA;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;kBACAC;gBACA;gBACAC;gBACAP;gBAAA,mBACAQ;gBACA;gBACAb;kBACAc;kBAAA;kBACAC;kBACAC;kBACAC;kBAAA;kBACAC;kBAAA;kBACAC;kBAAA;kBACAC;kBAAA;kBACAC;kBAAA;kBACAC;gBACA;;gBACA;kBAAAR;gBAAA;gBACAF;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACAF;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAa;MACA;QAAAC;QAAAC;MAAA;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACAC;gBACAjB;gBACAA;gBACAkB;gBACAA;gBACAA;gBACAlB;kBACA;kBACAmB;kBACA7B;gBACA;gBAAA,MACA2B;kBAAA;kBAAA;gBAAA;gBACA;gBACAG;gBACA;gBACA;gBACAC;kBACA;kBACA;kBACA;oBAAAP;oBAAAC;oBAAAO;kBAAA;gBACA;gBAAA;gBAAA;cAAA;gBAEA;gBAAA,eACAL;gBAAA,kCACA,6BAMA,6BAIA;gBAAA;cAAA;gBATA;gBACA;gBACA;gBACA;gBAAA;cAAA;gBAGA;gBACA;gBAAA;cAAA;gBAGA;gBACA;gBACA;gBAAA;cAAA;gBAGA;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAIA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9OA;AAAA;AAAA;AAAA;AAAw5C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACA56C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesGoEasy/chat_page/components/open-red-packet/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=6471714e&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=6471714e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6471714e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesGoEasy/chat_page/components/open-red-packet/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=6471714e&scoped=true&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"\">\n\t\t<uni-popup ref=\"popup\" type=\"center\" maskBackgroundColor=\"rgba(255, 255, 255, 0.7)\">\n\t\t\t<view class=\"popup-box\">\n\t\t\t\t<view class=\"popup\">\n\t\t\t\t\t<view class=\"z_index2 flex_c_c popup-top\">\n\t\t\t\t\t\t<view class=\"popup-top-str\">\n\t\t\t\t\t\t\t<view class=\"popup-top-str-img\" v-if=\"pageObj.payload\">\n\t\t\t\t\t\t\t\t<!-- #ifdef APP -->\n\t\t\t\t\t\t\t\t<cacheImage\n\t\t\t\t\t\t\t\t\t:src=\"pageObj.payload.red_packet_bg\"\n\t\t\t\t\t\t\t\t\text=\"jpg\"\n\t\t\t\t\t\t\t\t\tmstyle=\"\n\t\t\t\t\t\t\t\t\t\t {\n\t\t\t\t\t\t\t\t\t\t\twidth: 580rpx;\n\t\t\t\t\t\t\t\t\t\t\theight: 800rpx;\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\"\n\t\t\t\t\t\t\t\t></cacheImage>\n\t\t\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t\t\t\t<!-- #ifndef APP -->\n\t\t\t\t\t\t\t\t<image class=\"img\" :src=\"pageObj.payload.red_packet_bg\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"icon_ z_index2 info\">\n\t\t\t\t\t\t\t<view class=\"info-img\" style=\"flex-shrink: 0\">\n\t\t\t\t\t\t\t\t<image class=\"img\" :src=\"pageObj.senderData.avatar\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"text_30 info-name\">{{ pageObj.senderData.name }} 发出的红包</view>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<view class=\"z_index2 popup-title\" v-if=\"pageObj.is_refund\">红包已过期</view>\n\n\t\t\t\t\t\t<view class=\"text_36 z_index2 popup-title\" v-else>\n\t\t\t\t\t\t\t<view v-if=\"pageObj.had_draw\" class=\"text_50 bold_\">\n\t\t\t\t\t\t\t\t{{ pageObj.my_red }}\n\t\t\t\t\t\t\t\t<text class=\"text_22\">蝌蚪</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<text>{{ pageObj.remark }}</text>\n\t\t\t\t\t\t\t<!-- 专属红包 -->\n\t\t\t\t\t\t\t<view v-if=\"pageObj.type === 2\">\n\t\t\t\t\t\t\t\t<text v-if=\"pageObj.is_end === 1\">xxxxxx 已领取</text>\n\t\t\t\t\t\t\t\t<text v-else>xxxx 的专属红包</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<!-- 普通拼手气红包 -->\n\t\t\t\t\t\t\t<view v-else>\n\t\t\t\t\t\t\t\t<text v-if=\"!pageObj.had_draw && pageObj.is_end === 1\">手慢了，红包派完了</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view style=\"height: 300rpx\"></view>\n\n\t\t\t\t\t\t<!-- 拼手气红包 -->\n\t\t\t\t\t\t<template v-if=\"!pageObj.is_refund\">\n\t\t\t\t\t\t\t<template v-if=\"pageObj.type === 1\">\n\t\t\t\t\t\t\t\t<view class=\"icon_ popup-top-icon\" :class=\"{ popup_top_icon: isOpen }\" @click=\"open_red_packet\" v-if=\"!pageObj.had_draw && !pageObj.is_end\">\n\t\t\t\t\t\t\t\t\t<image\n\t\t\t\t\t\t\t\t\t\tclass=\"img\"\n\t\t\t\t\t\t\t\t\t\tsrc=\"data:image/svg+xml;base64,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\"\n\t\t\t\t\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t\t\t\t\t></image>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</template>\n\n\t\t\t\t\t\t\t<template v-else>\n\t\t\t\t\t\t\t\t<view class=\"icon_ popup-top-icon\" :class=\"{ popup_top_icon: isOpen }\" @click=\"open_red_packet\" v-if=\"!pageObj.had_draw && myid === pageObj.to_uid\">\n\t\t\t\t\t\t\t\t\t<image\n\t\t\t\t\t\t\t\t\t\tclass=\"img\"\n\t\t\t\t\t\t\t\t\t\tsrc=\"data:image/svg+xml;base64,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\"\n\t\t\t\t\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t\t\t\t\t></image>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"popup-button\">\n\t\t\t\t\t\t<!-- 自己有强到；已经抢完；专属红包并且不是自己的 -->\n\t\t\t\t\t\t<view\n\t\t\t\t\t\t\tclass=\"text_28 icon_ popup-button-text\"\n\t\t\t\t\t\t\t@click=\"onOpen\"\n\t\t\t\t\t\t\tv-if=\"pageObj.had_draw || pageObj.is_end === 1 || (pageObj.had_draw === 1 && pageObj.type === 2)\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t查看领取详情\n\t\t\t\t\t\t\t<view class=\"popup-button-icon\">\n\t\t\t\t\t\t\t\t<image\n\t\t\t\t\t\t\t\t\tclass=\"img\"\n\t\t\t\t\t\t\t\t\tsrc=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTM0Ny42ODcgMTQ0LjE4OGwtNTIuNzYxIDUyLjIzOCAzMTMuOTI4IDMxNi4wODItMzE2LjU2OCAzMTMuNDIgNTIuMzE0IDUyLjY3MyAzNjkuMzIyLTM2NS42NjN6IiBmaWxsPSIjZmFlMWFhIiBkYXRhLXNwbS1hbmNob3ItaWQ9ImEzMTN4LnNlYXJjaF9pbmRleC4wLmkxMC4zN2RiM2E4MXZBSW5MVyIgY2xhc3M9InNlbGVjdGVkIi8+PC9zdmc+\"\n\t\t\t\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t\t\t\t></image>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"close\" @click=\"onClose\">\n\t\t\t\t\t<image\n\t\t\t\t\t\tclass=\"img\"\n\t\t\t\t\t\tsrc=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTUxMiAuMDA2QzIyOS4yMzMuMDA2LjAwNiAyMjkuMjMzLjAwNiA1MTJTMjI5LjIzMyAxMDIzLjk5MyA1MTIgMTAyMy45OTMgMTAyMy45OTQgNzk0Ljc2NyAxMDIzLjk5NCA1MTIgNzk0Ljc2Ny4wMDYgNTEyIC4wMDZ6bTAgOTYyLjU0OUMyNjMuNTYzIDk2Mi41NTUgNjEuNDQ1IDc2MC40MzcgNjEuNDQ1IDUxMlMyNjMuNTYzIDYxLjQ0NSA1MTIgNjEuNDQ1IDk2Mi41NTUgMjYzLjU2MyA5NjIuNTU1IDUxMiA3NjAuNDM3IDk2Mi41NTUgNTEyIDk2Mi41NTV6IiBmaWxsPSIjZDNhZDczIi8+PHBhdGggZD0iTTcwNy40OTggMjczLjA2M0w1MTIgNDY4LjU2MSAzMTYuNTAyIDI3My4wNjNsLTQzLjQ0OSA0My40MzlMNDY4LjU1MSA1MTIgMjczLjA1MyA3MDcuNDk3bDQzLjQ0OSA0My40NUw1MTIgNTU1LjQ0OWwxOTUuNDk4IDE5NS40OTggNDMuNDQ5LTQzLjQ1TDU1NS40NDkgNTEybDE5NS40OTgtMTk1LjQ5OHoiIGZpbGw9IiNkM2FkNzMiLz48L3N2Zz4=\"\n\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</uni-popup>\n\t</view>\n</template>\n<script>\nimport { 自己的信息 } from '@/TEST/index';\n// #ifdef APP\nimport cacheImage from '../cache-image/cache-image.vue';\n// #endif\nimport { to, show } from '@/utils/index.js';\n\nconst AudioContext = uni.createInnerAudioContext();\n// 这里放打开红包时的金币掉落音频\n// AudioContext.src = 'https://xxxxxxxxxxx.mp3';\nlet item = null;\nexport default {\n\tcomponents: {\n\t\t// #ifdef APP\n\t\tcacheImage\n\t\t// #endif\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tmyid: null,\n\t\t\tisOpen: false,\r\n\t\t\tpageObj:{\r\n\t\t\t\tsenderData:{}\r\n\t\t\t},\n\t\t\titem: {\n\t\t\t\tsenderData: {},\n\t\t\t\tpayload: {}\n\t\t\t}\n\t\t};\n\t},\n\tmethods: {\n\t\tonClose() {\n\t\t\tthis.$refs.popup.close();\n\t\t},\n\t\tasync open(e) {\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '加载中'\n\t\t\t});\r\n\t\t\tconsole.log(e);\n\t\t\titem = e;\n\t\t\tconst { member_id = '' } = 自己的信息;\n\t\t\tthis.myid = member_id;\n\t\t\tconst data = {\r\n\t\t\t\ttype:1,//1拼手气,2专属\n\t\t\t\tto_uid: 62413,\n\t\t\t\tred_money: '0.01',\n\t\t\t\tremark: '恭喜发财,大吉大利', //是否\n\t\t\t\tis_refund: 0, //是否过期\n\t\t\t\thad_draw: 0,//自己是否抢到\n\t\t\t\tis_end: 0,//是否还有\n\t\t\t\tmy_red: null,//自己领取了多少钱\n\t\t\t\tred_packet_bg: 'https://app.gdmlmh.cn/static/images/red/red_bg.jpg'//红包背景\n\t\t\t};\n\t\t\tthis.pageObj = { ...e, ...data, type: data.type };\r\n\t\t\tconsole.log(this.$refs.popup)\n\t\t\tthis.$refs.popup.open();\n\t\t\t// 没有了并且也没抢到\n\t\t\t// if (data.is_end === 1 && !data.had_draw) {\n\t\t\t// \te.isClick = 1;\n\t\t\t// \tuni.$emit('open_red_packet', '1');\n\t\t\t// \t// 强制更新试图\n\t\t\t// \tthis.$forceUpdate();\n\t\t\t// \tlet envelopeClickList = uni.getStorageSync('envelopeClickList') || [];\n\t\t\t// \tif (envelopeClickList.includes(item.messageId)) {\n\t\t\t// \t\t// 这里就不用改状态了，因为前面也就改了\n\t\t\t// \t} else {\n\t\t\t// \t\t// 没有就存\n\t\t\t// \t\tenvelopeClickList = envelopeClickList.slice(0, 300);\n\t\t\t// \t\tenvelopeClickList.push(item.messageId);\n\t\t\t// \t\tuni.setStorage({\n\t\t\t// \t\t\t//红包是否已点击\n\t\t\t// \t\t\tkey: 'envelopeClickList',\n\t\t\t// \t\t\tdata: envelopeClickList\n\t\t\t// \t\t});\n\t\t\t// \t}\n\t\t\t// }\n\t\t\tuni.hideLoading();\n\t\t},\n\n\t\tonOpen() {\n\t\t\tto('/pagesGoEasy/envelope_receive/index', { messageId: item.payload.message_id, group_id: item.id });\n\t\t\tthis.onClose();\n\t\t},\n\t\t// 点击开启\n\t\tasync open_red_packet() {\n\t\t\tthis.isOpen = true;\n\t\t\tconst code = '0';\n\t\t\tuni.$emit('open_red_packet', code);\n\t\t\tuni.$off('open_red_packet');\n\t\t\tlet envelopeClickList = uni.getStorageSync('envelopeClickList') || [];\n\t\t\tenvelopeClickList = envelopeClickList.slice(0, 300);\n\t\t\tenvelopeClickList.push(item.messageId);\n\t\t\tuni.setStorage({\n\t\t\t\t//红包是否已点击\n\t\t\t\tkey: 'envelopeClickList',\n\t\t\t\tdata: envelopeClickList\n\t\t\t});\n\t\t\tif (code === '0') {\n\t\t\t\t// 金币声音\n\t\t\t\tAudioContext.play();\n\t\t\t\t// 领取\n\t\t\t\t// 延时展示一下动画\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.onClose();\n\t\t\t\t\tthis.isOpen = false;\n\t\t\t\t\tto('/pagesGoEasy/envelope_receive/index', { messageId: item.payload.message_id, group_id: item.id, first: 1 });\n\t\t\t\t}, 600);\n\t\t\t} else {\n\t\t\t\tthis.isOpen = false;\n\t\t\t\tswitch (code) {\n\t\t\t\t\tcase '-2':\n\t\t\t\t\t\t// 已被领完\n\t\t\t\t\t\tthis.pageObj.is_end = 1;\n\t\t\t\t\t\tthis.pageObj.had_draw = 0;\n\t\t\t\t\t\tshow('手慢了！');\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase '-3':\n\t\t\t\t\t\t// 已过期\n\t\t\t\t\t\tshow('红包已过期');\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase '-5':\n\t\t\t\t\t\t// 已领取\n\t\t\t\t\t\tthis.pageObj.had_draw = 1;\n\t\t\t\t\t\tshow('已领取');\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tshow('红包异常');\n\t\t\t\t\t\tthis.onClose();\n\t\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.popup-box {\n\tposition: relative;\n\tmargin-bottom: 200rpx;\n\t.close {\n\t\tposition: absolute;\n\t\tbottom: -130rpx;\n\t\tleft: calc(50% - 35rpx);\n\t\twidth: 70rpx;\n\t\theight: 70rpx;\n\t}\n}\n.popup {\n\twidth: 580rpx;\n\theight: 1000rpx;\n\tborder-radius: 20rpx;\n\toverflow: hidden;\n\tbackground-color: #f35d4c;\n\t.popup-top {\n\t\tposition: relative;\n\t\twidth: 100%;\n\t\theight: 800rpx;\n\t\tborder-radius: 20rpx;\n\t\t.info {\n\t\t\tmargin: 0 auto;\n\t\t\twidth: calc(100% - 40rpx);\n\t\t\t.info-img {\n\t\t\t\twidth: 40rpx;\n\t\t\t\theight: 40rpx;\n\t\t\t\tbackground-color: #fff;\n\t\t\t\tmargin-right: 10rpx;\n\t\t\t\tborder-radius: 6rpx;\n\t\t\t\toverflow: hidden;\n\t\t\t}\n\t\t\t.info-name {\n\t\t\t\tcolor: #fae1aa;\n\t\t\t}\n\t\t}\n\t\t.popup-title {\n\t\t\tmargin-top: 50rpx;\n\t\t\ttext-align: center;\n\t\t\tcolor: #fae1aa;\n\t\t}\n\n\t\t.popup-top-str {\n\t\t\tposition: absolute;\n\t\t\tleft: calc(50% - 600rpx);\n\t\t\tbottom: 0rpx;\n\t\t\twidth: 1200rpx;\n\t\t\theight: 1100rpx;\n\t\t\tborder-radius: 50%;\n\t\t\toverflow: hidden;\n\t\t\tbox-shadow: 0rpx 4rpx 10rpx rgba(0, 0, 0, 0.2);\n\t\t\t.popup-top-str-img {\n\t\t\t\tposition: absolute;\n\t\t\t\tleft: calc(50% - 290rpx);\n\t\t\t\tbottom: 0;\n\t\t\t\twidth: 580rpx;\n\t\t\t\theight: 800rpx;\n\t\t\t}\n\t\t}\n\t\t.popup-top-icon {\n\t\t\tposition: absolute;\n\t\t\tbottom: -90rpx;\n\t\t\tleft: calc(50% - 90rpx);\n\t\t\twidth: 180rpx;\n\t\t\theight: 180rpx;\n\t\t\tbackground-color: #eccc99;\n\t\t\tborder-radius: 50%;\n\t\t\tbox-shadow: 0rpx 2rpx 6rpx rgba(0, 0, 0, 0.2);\n\t\t\t.img {\n\t\t\t\twidth: 40%;\n\t\t\t\theight: 40%;\n\t\t\t}\n\t\t}\n\t\t.popup_top_icon {\n\t\t\tanimation: animateName 1.5s infinite;\n\t\t}\n\t}\n\t.popup-button {\n\t\tposition: relative;\n\t\tz-index: 1;\n\t\twidth: 100%;\n\t\theight: 200rpx;\n\t\tborder-radius: 0 0 20rpx 20rpx;\n\t\t.popup-button-text {\n\t\t\tposition: absolute;\n\t\t\theight: 40rpx;\n\t\t\twidth: 100%;\n\t\t\tbottom: 40rpx;\n\t\t\tleft: 0;\n\t\t\tcolor: #fae1aa;\n\t\t\t.popup-button-icon {\n\t\t\t\twidth: 40rpx;\n\t\t\t\theight: 40rpx;\n\t\t\t}\n\t\t}\n\t}\n}\n@keyframes animateName {\n\t0% {\n\t\ttransform: rotateY(0deg);\n\t}\n\t50% {\n\t\ttransform: rotateY(180deg);\n\t}\n\t100% {\n\t\ttransform: rotateY(0deg);\n\t}\n}\n</style>\n", "import mod from \"-!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=6471714e&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=6471714e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754983153463\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}