{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/envelope_receive/index.vue?f82c", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/envelope_receive/index.vue?533a", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/envelope_receive/index.vue?a701", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/envelope_receive/index.vue?1e18", "uni-app:///pagesGoEasy/envelope_receive/index.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/envelope_receive/index.vue?b7dd", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/envelope_receive/index.vue?0b1b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "data", "loadingState", "pageObj", "member_info", "page", "list", "onLoad", "red", "create_time", "name", "avatar", "member_id", "onReachBottom", "computed", "page_font_size", "statusBar", "customBar", "titleText", "best", "secondsToHms", "methods", "to", "formatDate"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACwL;AACxL,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4KAEN;AACP,KAAK;AACL;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,aAAa,wRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7DA;AAAA;AAAA;AAAA;AAA2sB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC0H/tB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;;AAEA;AACA;AAAA,eACA;EACAC,aAIA;EACAC;IACA;MACAC;MACAC;QACAC;MACA;MACAC;MACAC;IACA;EACA;EACAC;IACA,aACA;MACAC;MACAC;MACAL;QACAM;QACAC;QACAC;MACA;IACA,GACA;MACAJ;MACAC;MACAL;QACAM;QACAC;QACAC;MACA;IACA,GACA;MACAJ;MACAC;MACAL;QACAM;QACAC;QACAC;MACA;IACA,EACA;EACA;EACAC;EAEAC;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;EACAC;IACAC;IACAC;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChNA;AAAA;AAAA;AAAA;AAAk2C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACAt3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesGoEasy/envelope_receive/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesGoEasy/envelope_receive/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=382407c6&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=382407c6&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"382407c6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesGoEasy/envelope_receive/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=382407c6&scoped=true&\"", "var components\ntry {\n  components = {\n    mLine: function () {\n      return import(\n        /* webpackChunkName: \"components/m-line/m-line\" */ \"@/components/m-line/m-line.vue\"\n      )\n    },\n    mBottomPaceholder: function () {\n      return import(\n        /* webpackChunkName: \"components/m-bottom-paceholder/m-bottom-paceholder\" */ \"@/components/m-bottom-paceholder/m-bottom-paceholder.vue\"\n      )\n    },\n    mScreenAnimationLihua: function () {\n      return import(\n        /* webpackChunkName: \"components/m-screen-animation-lihua/m-screen-animation-lihua\" */ \"@/components/m-screen-animation-lihua/m-screen-animation-lihua.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.list, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = _vm.formatDate(item.create_time)\n    var m1 = _vm.best(index)\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view :id=\"page_font_size\">\n\t\t<view class=\"navigationBar-box\">\n\t\t\t<!-- #ifdef MP -->\n\t\t\t<view :style=\"{ height: customBar - statusBar + 'px' }\"></view>\n\t\t\t<!-- #endif -->\n\t\t\t<!-- #ifndef MP -->\n\t\t\t<view :style=\"{ height: statusBar + 'px' }\"></view>\n\t\t\t<!-- #endif -->\n\n\t\t\t<view class=\"flex_r fa_c fj_b navigationBar\" :style=\"{ height: customBar - statusBar + 'px' }\">\n\t\t\t\t<view class=\"icon_ navigationBar-icon\" @click=\"to()\">\n\t\t\t\t\t<image\n\t\t\t\t\t\tclass=\"img\"\n\t\t\t\t\t\tsrc=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTcwNy4zMjMgOTYwLjU1Nmw1Ni4wOTMtNTQuNTAzLTQwMy45MTctMzkyLjQ2OSA0MDMuOTE3LTM5Mi40NzUtNTYuMDkzLTU0LjUwMkwyNDcuMzIgNTEzLjU4NGw0NjAuMDA0IDQ0Ni45NzJ6bTAgMHoiIGZpbGw9IiNmZmYiLz48L3N2Zz4=\"\n\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"flex1 icon_ navigationBar-text bold_\"></view>\n\t\t\t\t<view class=\"navigationBar-icon\"></view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"top\">\n\t\t\t<view class=\"top-str\">\n\t\t\t\t<view class=\"top-str-bc\">\n\t\t\t\t\t<!-- #ifdef APP -->\n\t\t\t\t\t<cacheImage\n\t\t\t\t\t\t:src=\"pageObj.red_packet_bg\"\n\t\t\t\t\t\text=\"jpg\"\n\t\t\t\t\t\tmstyle=\"\n\t\t\t\t\t\t\t {\n\t\t\t\t\t\t\t\twidth: 100vw;\n\t\t\t\t\t\t\t\theight: 420rpx;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\"\n\t\t\t\t\t></cacheImage>\n\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t<!-- #ifndef APP -->\n\t\t\t\t\t<image class=\"img\" :src=\"pageObj.red_packet_bg\" mode=\"aspectFill\"></image>\n\t\t\t\t\t<!-- #endif -->\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view style=\"height: 420rpx\"></view>\n\t\t<view class=\"icon_ info\">\n\t\t\t<view class=\"info-img\" style=\"flex-shrink: 0\">\n\t\t\t\t<image class=\"img\" src=\"https://tse4-mm.cn.bing.net/th/id/OIP-C.duz6S7Fvygrqd6Yj_DcXAQHaF7?rs=1&pid=ImgDetMain\" mode=\"aspectFill\"></image>\n\t\t\t</view>\n\t\t\t<view class=\"text_34 bold_ info-name\">荷塘月色 发出的红包</view>\n\t\t</view>\n\t\t<view class=\"text_28 color__ icon_ title\">恭喜发财 大吉大利</view>\n\n\t\t<!-- 金额 -->\n\t\t<template>\n\t\t\t<view class=\"flex_r money\">\n\t\t\t\t<view class=\"money-value\">\n\t\t\t\t\t<text class=\"bold_ money-value-\">100.00</text>\n\t\t\t\t\t<view class=\"text_28 money-text\">￥</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"icon_ text_26 money-illustrate\" @click=\"to('/pagesUser/user/account/index')\">\n\t\t\t\t已存入账户，可用于购买商品\n\t\t\t\t<view class=\"icon_ money-illustrate-icon\">\n\t\t\t\t\t<image\n\t\t\t\t\t\tclass=\"img\"\n\t\t\t\t\t\tsrc=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTQ3Ny44IDgxNi40bDMwNC4xLTMwNC42LTMwMy44LTMwMy4yYy0xOC41LTE4LjUtMTguNS00OC41IDAtNjcuMSAxOC41LTE4LjYgNDguNi0xOC42IDY3LjEtLjFsMzM3LjMgMzM2LjdjMTguNSAxOC41IDE4LjUgNDguNSAwIDY3LjFMNTQ0LjkgODgzLjRjLTE4LjUgMTguNi00OC42IDE4LjYtNjcuMS4xLTE4LjUtMTguNS0xOC41LTQ4LjUgMC02Ny4xeiIgZmlsbD0iI2QzYWQ3MyIgZGF0YS1zcG0tYW5jaG9yLWlkPSJhMzEzeC5zZWFyY2hfaW5kZXguMC5pMjEuMzdkYjNhODF2QUluTFciIGNsYXNzPSJzZWxlY3RlZCIvPjwvc3ZnPg==\"\n\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</template>\n\t\t<!-- <view class=\"interval\"></view> -->\n\n\t\t<view class=\"list\">\n\t\t\t<view class=\"text_30 list-title\">{{ titleText }}</view>\n\t\t\t<m-line color=\"#e5e5e5\" margin=\"0 0 30rpx 0\" length=\"100%\" :hairline=\"true\"></m-line>\n\t\t\t<view\n\t\t\t\tclass=\"flex_r item\"\n\t\t\t\tv-for=\"(item, index) in list\"\n\t\t\t\t:key=\"item.id\"\n\t\t\t\t@click=\"to('/pagesGoEasy/group_member_infor/index', { member_id: item.member_id, group_id: item.group_id })\"\n\t\t\t>\n\t\t\t\t<view class=\"item-img\">\n\t\t\t\t\t<image class=\"img\" :src=\"item.member_info.avatar\" mode=\"aspectFill\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"flex1 item-r\">\n\t\t\t\t\t<view class=\"flex_r text_30 item-name\">\n\t\t\t\t\t\t<view class=\"flex1\">{{ item.member_info.name }}</view>\n\t\t\t\t\t\t<view class=\"\">\n\t\t\t\t\t\t\t<text class=\"bold_\">{{ item.red }}</text>\n\t\t\t\t\t\t\t<text class=\"color__ text_18\" style=\"margin-left: 6rpx\">￥</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"text_22 flex_r fj_b item-time\">\n\t\t\t\t\t\t<text>{{ formatDate(item.create_time) }}</text>\n\t\t\t\t\t\t<view class=\"icon_ label\" v-if=\"best(index)\">\n\t\t\t\t\t\t\t<view class=\"label-icon\">\n\t\t\t\t\t\t\t\t<image\n\t\t\t\t\t\t\t\t\tclass=\"img\"\n\t\t\t\t\t\t\t\t\tsrc=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTAgMGgxMDI0djEwMjRIMHoiIGZpbGw9Im5vbmUiLz48cGF0aCBkPSJNOTg1LjYgMzUyYzAtNDAuMzItMzItNzIuMzItNzEuMDQtNzIuMzItMzkuNjggMC03MS4wNCAzMi42NC03MS4wNCA3Mi4zMiAwIDE1LjM2IDQuNDggMjkuNDQgMTIuOCA0MS42LTQzLjUyIDc0LjI0LTc0Ljg4IDE3NC43Mi0xMzUuNjggMTc0LjcyLTkuNiAwLTE5LjItLjY0LTI4LjE2LTIuNTYtOTYtMTcuMjgtMTQ3LjItMjUzLjQ0LTE1Mi4zMi0yODYuNzIgMjMuMDQtMTIuMTYgMzkuNjgtMzcuMTIgMzkuNjgtNjUuOTIgMC00MC4zMi0zMi03Mi4zMi03MS4wNC03Mi4zMmE3Mi41MTIgNzIuNTEyIDAgMCAwLTI0Ljk2IDE0MC4xNmMtNS4xMiAzNy4xMi01My43NiAyNzYuNDgtMTQ3LjIgMjg5LjkyLTcuNjggMS4yOC0xNS4zNiAxLjkyLTIzLjY4IDEuOTItNjAuMTYgMC0xMDIuNC0xMDQuOTYtMTM3LjYtMTc0LjcyIDMuODQtOC4zMiA1Ljc2LTE3LjkyIDUuNzYtMjguMTYgMC00MC4zMi0zMi03Mi4zMi03MS4wNC03Mi4zMi0xOS4yIDAtMzYuNDggNy42OC00OS4yOCAyMC40OC0xNC4wOCAxMy40NC0yMi40IDMyLTIyLjQgNTEuODQgMCA0MC4zMiAzMiA3Mi4zMiA3MS4wNCA3Mi4zMiAxMjQuMTYgMzEwLjQgMTg1LjYgNDY1LjkyIDE4NS42IDQ2NS45MlM0MDEuOTIgOTYwIDUxMS4zNiA5NjBzMjE3LjYtNTEuODQgMjE3LjYtNTEuODQgNjQuNjQtMTYxLjI4IDE5My4yOC00ODQuNDhjMzUuMi0zLjg0IDYzLjM2LTM0LjU2IDYzLjM2LTcxLjY4eiIgZmlsbD0iI0ZCRDMwRiIvPjxwYXRoIGQ9Ik0yOTQuNCA5MDQuMzJjMCAyOS40NCAxMDYuODggNTkuNTIgMjE1LjA0IDU5LjUyUzcyOS42IDkzNC40IDcyOS42IDkwNC4zMmMwLTI5LjQ0LTExMi42NC01OS41Mi0yMjAuMTYtNTkuNTJTMjk0LjQgODc0LjI0IDI5NC40IDkwNC4zMnoiIGZpbGw9IiNGRkE3MDYiLz48L3N2Zz4=\"\n\t\t\t\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t\t\t\t></image>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t手气最佳\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"item-r-m-line\">\n\t\t\t\t\t\t<m-line color=\"#e5e5e5\" length=\"100%\" :hairline=\"true\"></m-line>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<m-bottom-paceholder></m-bottom-paceholder>\n\t\t<!-- 礼花 -->\n\t\t<m-screen-animation-lihua ref=\"mScreenAnimationLihua\" zIndex=\"9999\"></m-screen-animation-lihua>\n\t</view>\n</template>\n<script>\n// #ifdef APP\nimport cacheImage from '@/pagesGoEasy/chat_page/components/cache-image/cache-image.vue';\n// #endif\nimport { to, jsonUrl, formatDate, getAudioContext } from '@/utils/index.js';\nimport { mapState } from 'vuex';\nlet data = null;\n\n// 到账声音\nconst envelope_receive_audio_context = uni.createInnerAudioContext();\nexport default {\n\tcomponents: {\n\t\t// #ifdef APP\n\t\tcacheImage\n\t\t// #endif\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tloadingState: 'lom',\n\t\t\tpageObj: {\n\t\t\t\tmember_info: {}\n\t\t\t},\n\t\t\tpage: 1,\n\t\t\tlist: []\n\t\t};\n\t},\n\tonLoad(e) {\n\t\tthis.list = [\n\t\t\t{\n\t\t\t\tred: '0.01',\n\t\t\t\tcreate_time: '2024-07-29 14:01:04',\n\t\t\t\tmember_info: {\n\t\t\t\t\tname: '明天会更好',\n\t\t\t\t\tavatar: 'https://tse4-mm.cn.bing.net/th/id/OIP-C.NimIzUOhgk2QHjPwRE0Q8gHaE5?rs=1&pid=ImgDetMain',\n\t\t\t\t\tmember_id: 105974\n\t\t\t\t}\n\t\t\t},\n\t\t\t{\n\t\t\t\tred: '100.00',\n\t\t\t\tcreate_time: '2024-07-29 14:01:04',\n\t\t\t\tmember_info: {\n\t\t\t\t\tname: '复兴中华',\n\t\t\t\t\tavatar: 'https://img.zcool.cn/community/014cdd5a96ba16a801219586209ded.png@1280w_1l_2o_100sh.png',\n\t\t\t\t\tmember_id: 87253\n\t\t\t\t}\n\t\t\t},\n\t\t\t{\n\t\t\t\tred: '0.1',\n\t\t\t\tcreate_time: '2024-07-29 14:01:04',\n\t\t\t\tmember_info: {\n\t\t\t\t\tname: '运筹帷幄',\n\t\t\t\t\tavatar: 'https://img.zcool.cn/community/0121e25a98e23aa801206d96c1cf46.jpg@1280w_1l_2o_100sh.jpg',\n\t\t\t\t\tmember_id: 87253\n\t\t\t\t}\n\t\t\t}\n\t\t];\n\t},\n\tonReachBottom() {},\n\n\tcomputed: mapState({\n\t\tpage_font_size: (state) => state.page_font_size,\n\t\tstatusBar: (state) => state.StatusBar.statusBar,\n\t\tcustomBar: (state) => state.StatusBar.customBar,\n\t\ttitleText() {\n\t\t\tif (!this.pageObj.red_num) return '';\n\t\t\tif (this.pageObj.type === 2 && this.pageObj.is_end) return `专属红包，${this.secondsToHms}被领取`;\n\t\t\tif (this.pageObj.is_end) return `${this.pageObj.red_num}个红包，${this.secondsToHms}被抢光`;\n\t\t\treturn `领取${this.pageObj.has_num}/${this.pageObj.red_num}个`;\n\t\t},\n\t\t// 判断手气最佳\n\t\tbest() {\n\t\t\treturn (index) => {\n\t\t\t\treturn index === 1;\n\t\t\t};\n\t\t},\n\t\tsecondsToHms() {\n\t\t\tlet d = Number(this.pageObj.time);\n\t\t\tvar h = Math.floor(d / 3600);\n\t\t\tvar m = Math.floor((d % 3600) / 60);\n\t\t\tvar s = Math.floor((d % 3600) % 60);\n\t\t\tvar hDisplay = h > 0 ? h + (h == 1 ? '小时' : '小时') : '';\n\t\t\tvar mDisplay = m > 0 ? m + (m == 1 ? '分钟' : '分钟') : '';\n\t\t\tvar sDisplay = s > 0 ? s + (s == 1 ? '秒' : '秒') : '';\n\t\t\treturn `${hDisplay}${mDisplay}${sDisplay}`;\n\t\t}\n\t}),\n\tmethods: {\n\t\tto,\n\t\tformatDate\n\t}\n};\n</script>\n\n<style scoped lang=\"scss\">\n.navigationBar-box {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\tz-index: 99;\n\t.navigationBar {\n\t\twidth: 100%;\n\t\t.navigationBar-icon {\n\t\t\twidth: 44rpx;\n\t\t\theight: 44rpx;\n\t\t\tmargin: 0 30rpx;\n\t\t\t.img {\n\t\t\t\twidth: 90%;\n\t\t\t\theight: 90%;\n\t\t\t}\n\t\t}\n\t\t.navigationBar-text {\n\t\t}\n\t}\n}\n.top {\n\tposition: fixed;\n\tz-index: 3;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 420rpx;\n\tbackground-color: #fff;\n\t.top-str {\n\t\tbox-sizing: border-box;\n\t\tposition: absolute;\n\t\tleft: calc(50% - 600rpx);\n\t\tbottom: 30rpx;\n\t\twidth: 1200rpx;\n\t\theight: 800rpx;\n\t\tborder-radius: 50%;\n\t\tbackground-color: #f25745;\n\t\tborder: 6rpx solid #f8ca75;\n\t\toverflow: hidden;\n\t\t.top-str-bc {\n\t\t\tposition: absolute;\n\t\t\tleft: calc(50% - 50vw);\n\t\t\tbottom: 0;\n\t\t\tz-index: 2;\n\t\t\twidth: 100vw;\n\t\t\theight: 420rpx;\n\t\t}\n\t}\n}\n.info {\n\tbox-sizing: border-box;\n\tpadding: 0 40rpx;\n\twidth: 100%;\n\tmin-height: 60rpx;\n\t.info-img {\n\t\twidth: 50rpx;\n\t\theight: 50rpx;\n\t\tborder-radius: 10rpx;\n\t\toverflow: hidden;\n\t\tmargin-right: 10rpx;\n\t\tbackground-color: #f4f4f4;\n\t}\n\t.info-name {\n\t}\n}\n\n.title {\n\tbox-sizing: border-box;\n\tpadding: 0 40rpx;\n\twidth: 100%;\n\tmin-height: 60rpx;\n}\n.money {\n\twidth: 100%;\n\theight: 100rpx;\n\tmargin-top: 40rpx;\n\tcolor: #c2a26f;\n\talign-items: flex-end;\n\tjustify-content: center;\n\t.money-value {\n\t\tposition: relative;\n\t\t.money-value- {\n\t\t\tfont-size: 90rpx;\n\t\t}\n\t\t.money-text {\n\t\t\tposition: absolute;\n\t\t\tright: -70rpx;\n\t\t\tbottom: 20rpx;\n\t\t}\n\t}\n}\n.money-illustrate {\n\twidth: 100%;\n\tcolor: #c2a26f;\n\theight: 40rpx;\n\tmargin-top: 10rpx;\n\t.money-illustrate-icon {\n\t\twidth: 30rpx;\n\t\theight: 30rpx;\n\t}\n}\n.interval {\n\twidth: 100%;\n\theight: 16rpx;\n\tmargin-top: 60rpx;\n\tbackground-color: #e5e5e5;\n}\n.list {\n\tbox-sizing: border-box;\n\tpadding: 0 30rpx;\n\twidth: 100%;\n\tmargin-top: 40rpx;\n\t.list-title {\n\t\theight: 80rpx;\n\t\tline-height: 80rpx;\n\t\tcolor: #b2b2b2;\n\t}\n\t.item {\n\t\twidth: 100%;\n\t\theight: 100rpx;\n\t\tmargin-bottom: 20rpx;\n\t\t.item-img {\n\t\t\twidth: 80rpx;\n\t\t\theight: 80rpx;\n\t\t\tmargin-right: 20rpx;\n\t\t\tborder-radius: 10rpx;\n\t\t\toverflow: hidden;\n\t\t\tbackground-color: #f1f1f1;\n\t\t}\n\t\t.item-r {\n\t\t\t.item-name {\n\t\t\t\theight: 50rpx;\n\t\t\t}\n\t\t\t.item-time {\n\t\t\t\theight: 34rpx;\n\t\t\t\tcolor: #b2b2b2;\n\t\t\t\t.label {\n\t\t\t\t\tcolor: #edb746;\n\t\t\t\t\t.label-icon {\n\t\t\t\t\t\twidth: 34rpx;\n\t\t\t\t\t\theight: 34rpx;\n\t\t\t\t\t\tmargin-right: 10rpx;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t.item-r-m-line {\n\t\t\t\twidth: 100%;\n\t\t\t\tmargin-top: 20rpx;\n\t\t\t\tmargin-bottom: 10rpx;\n\t\t\t}\n\t\t}\n\t}\n}\n</style>\n", "import mod from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=382407c6&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=382407c6&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754983153506\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}