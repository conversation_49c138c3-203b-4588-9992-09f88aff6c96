<template>
  <view class="chat-container">
    <!-- 顶部导航栏 -->
    <view class="chat-header">
      <view class="header-left" @click="goBack">
        <text class="back-icon">‹</text>
      </view>
      <view class="header-center">
        <text class="chat-title">{{ chatTitle }}</text>
        <text class="online-status" v-if="isOnline">在线</text>
      </view>
      <view class="header-right" @click="showChatMenu">
        <text class="more-icon">⋯</text>
      </view>
    </view>

    <!-- 消息列表 -->
    <scroll-view
      class="message-list"
      scroll-y
      scroll-with-animation
      :scroll-top="scrollTop"
      @scrolltoupper="loadMoreMessages"
      :enable-back-to-top="true"
    >
      <!-- 加载更多指示器 -->
      <view class="load-more" v-if="loading">
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 消息内容 -->
      <view class="message-content">
        <block v-for="(item, index) in messageList" :key="item.id || index">
          <!-- 时间分隔线 -->
          <view class="time-divider" v-if="item.showCreateTime">
            <text class="time-text">{{ formatTime(item.createTime) }}</text>
          </view>

          <!-- 消息气泡 -->
          <view class="message-item" :class="getMessageClass(item)">
            <!-- 左侧头像（对方消息） -->
            <view class="avatar-container" v-if="!isSelfMessage(item)">
              <image :src="getUserAvatar(item.userId)" class="avatar" mode="aspectFill" @click="showUserProfile(item.userId)" />
            </view>

            <!-- 消息主体 -->
            <view class="message-body">
              <!-- 昵称（对方消息） -->
              <view class="nickname" v-if="!isSelfMessage(item) && showNickname(item, index)">
                {{ getUserNickname(item.userId) }}
              </view>

              <!-- 消息内容 -->
              <view class="message-bubble" @click="handleMessageClick(item)" @longpress="showMessageMenu(item)">
                <!-- 文本消息 -->
                <view class="text-content" v-if="item.msgType === 'text'">
                  {{ item.content }}
                </view>

                <!-- 图片消息 -->
                <view class="image-content" v-else-if="item.msgType === 'image'">
                  <image :src="item.content" mode="aspectFill" class="message-image" @click="previewImage(item.content)" />
                </view>

                <!-- 语音消息 -->
                <view class="voice-content" v-else-if="item.msgType === 'voice'" :class="{ playing: item.isPlaying }">
                  <image :src="getVoiceIcon(item)" class="voice-icon" :class="{ 'voice-animation': item.isPlaying }" />
                  <text class="voice-duration">{{ getVoiceDuration(item) }}</text>
                </view>

                <!-- 消息状态 -->
                <view class="message-status" v-if="isSelfMessage(item)">
                  <text class="status-icon" v-if="item.status === 'sending'">⏳</text>
                  <text class="status-icon" v-else-if="item.status === 'failed'">❌</text>
                  <text class="status-icon" v-else-if="item.status === 'sent'">✓</text>
                  <text class="status-icon read" v-else-if="item.status === 'read'">✓✓</text>
                </view>
              </view>
            </view>

            <!-- 右侧头像（自己的消息） -->
            <view class="avatar-container" v-if="isSelfMessage(item)">
              <image :src="selfAvatar" class="avatar" mode="aspectFill" />
            </view>
          </view>
        </block>
      </view>

      <!-- 底部占位 -->
      <view class="bottom-placeholder"></view>
    </scroll-view>

    <!-- 输入工具栏 -->
    <view class="input-toolbar" :style="{ bottom: keyboardHeight + 'px' }">
      <!-- 语音录制模式 -->
      <view class="voice-mode" v-if="inputMode === 'voice'">
        <view class="voice-controls">
          <text class="mode-switch" @click="switchToTextMode">文</text>
          <view
            class="voice-button"
            :class="{ recording: isRecording }"
            @touchstart="startRecording"
            @touchend="stopRecording"
            @touchcancel="cancelRecording"
          >
            <text class="voice-text">{{ voiceButtonText }}</text>
          </view>
        </view>
      </view>

      <!-- 文本输入模式 -->
      <view class="text-mode" v-else>
        <view class="input-controls">
          <!-- 语音按钮 -->
          <text class="voice-btn" @click="switchToVoiceMode">🎤</text>

          <!-- 文本输入框 -->
          <view class="input-wrapper">
            <textarea
              v-model="inputText"
              class="text-input"
              placeholder="输入消息..."
              :focus="inputFocus"
              :auto-height="true"
              :max-height="120"
              @focus="handleInputFocus"
              @blur="handleInputBlur"
              @input="handleInput"
              @confirm="sendTextMessage"
            />
          </view>

          <!-- 表情按钮 -->
          <text class="emoji-btn" @click="toggleEmojiPanel">😊</text>

          <!-- 更多按钮 -->
          <text class="more-btn" @click="toggleMorePanel" v-if="!inputText.trim()">+</text>

          <!-- 发送按钮 -->
          <view class="send-btn" v-if="inputText.trim()" @click="sendTextMessage">
            <text class="send-text">发送</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 表情面板 -->
    <view class="emoji-panel" v-if="showEmojiPanel">
      <!-- 表情内容 -->
    </view>

    <!-- 更多功能面板 -->
    <view class="more-panel" v-if="showMorePanel">
      <view class="more-grid">
        <view class="more-item" @click="chooseImage">
          <text class="more-icon">📷</text>
          <text class="more-text">相册</text>
        </view>
        <view class="more-item" @click="takePhoto">
          <text class="more-icon">📸</text>
          <text class="more-text">拍照</text>
        </view>
        <view class="more-item" @click="chooseVideo">
          <text class="more-icon">🎬</text>
          <text class="more-text">视频</text>
        </view>
        <view class="more-item" @click="chooseFile">
          <text class="more-icon">📁</text>
          <text class="more-text">文件</text>
        </view>
      </view>
    </view>

    <!-- 语音录制动画 -->
    <view class="recording-overlay" v-if="isRecording">
      <view class="recording-animation">
        <view class="wave-container">
          <view class="wave" v-for="i in 5" :key="i" :style="{ animationDelay: i * 0.1 + 's' }"></view>
        </view>
        <text class="recording-text">{{ recordingText }}</text>
        <text class="recording-tip">松开发送，上滑取消</text>
      </view>
    </view>
  </view>
</template>

<script>
// 录音相关导入
import Recorder from 'recorder-core'
import RecordApp from 'recorder-core/src/app-support/app'
import '@/uni_modules/Recorder-UniCore/app-uni-support.js'

// #ifdef H5 || MP-WEIXIN
import 'recorder-core/src/engine/mp3'
import 'recorder-core/src/engine/mp3-engine'
import 'recorder-core/src/extensions/waveview'
// #endif

// 新的MQTT工具包
import mqttClient from '@/utils/mqttClient.js'
import { createUserInfo, createChatMessage, MESSAGE_TYPES, TOPIC_TEMPLATES } from '@/utils/mqttConfig.js'
import mqtt from '@/utils/mqtt.min.js'

// API和工具
import { msglist } from '@/api/public.js'
import store from '@/store'
import Cache from '@/utils/cache.js'
import { SplitArray } from '@/utils'

// 录音管理器
const recorderManager = uni.getRecorderManager()

export default {
  data() {
    return {
      // 用户信息
      selfUserId: store.state.app.userInfo.userId,
      selfAvatar: store.state.app.userInfo.avatar,
      userMap: {},

      // 聊天信息
      groupId: '',
      chatTitle: '',
      isOnline: false,

      // 消息列表
      list: [],
      page: 1,
      pageSize: 50,
      loading: false,
      loadend: false,

      // 滚动相关
      scrollTop: 0,

      // 输入相关
      inputMode: 'text', // 'text' | 'voice'
      inputText: '',
      inputFocus: false,
      keyboardHeight: 0,

      // 面板状态
      showEmojiPanel: false,
      showMorePanel: false,

      // 语音录制
      isRecording: false,
      recordingText: '正在录音...',
      voiceButtonText: '按住 说话',

      // 旧版兼容
      content: '',
      messageType: 'text',
      recordStart: false,
      top: 0,
      focus: false,
      isMounted: false,
      music: null,

      // MQTT相关（将使用新的工具包）
      mqttClientInstance: null
    }
  },

  computed: {
    /**
     * 格式化后的消息列表
     */
    messageList() {
      return this.formatMessages(this.list)
    }
  },

  onUnload() {
    // 断开MQTT连接
    if (this.mqttClientInstance) {
      mqttClient.disconnect()
    }

    // 清理音频播放
    if (this.music) {
      this.music.destroy()
      this.music = null
    }
  },

  onLoad(options) {
    console.log('页面参数:', options)
    console.log('用户信息:', store.state.app.userInfo)
    // 设置页面标题和基本信息
    this.chatTitle = options.name || '聊天'
    uni.setNavigationBarTitle({ title: this.chatTitle })
    // 初始化用户映射
    this.userMap = JSON.parse(Cache.get('userMap') || '{}')
    console.log('用户映射:', this.userMap)
    // 设置群组ID
    this.groupId = options.groupId
    // 兼容旧版本
    this._friendAvatar = options.avatar
    this._selfAvatar = store.state.app.userInfo.avatar
    // 加载消息数据
    this.loadData()
    // 初始化MQTT连接
    this.initMqttConnection()
  },
  onShow() {
    //onShow可能比mounted先执行，页面可能还未准备好
    if (this.isMounted) RecordApp.UniPageOnShow(this)
  },
  computed: {
    messageList() {
      console.log('🚀 ~ messageList ~ this.formatMessages(this.list):', this.formatMessages(this.list))
      return this.formatMessages(this.list)
    }
  },
  onHide() {
    if (this._innerAudioContext) {
      this._innerAudioContext.stop()
    }
  },
  mounted() {
    this.isMounted = true
    //App的renderjs必须调用的函数，传入当前模块this
    RecordApp.UniRenderjsRegister(this)
    const stream = navigator.mediaDevices.getUserMedia({ audio: true })
  },
  methods: {
    // ==================== 新增的UI交互方法 ====================

    /**
     * 返回上一页
     */
    goBack() {
      uni.navigateBack()
    },

    /**
     * 显示聊天菜单
     */
    showChatMenu() {
      uni.showActionSheet({
        itemList: ['清空聊天记录', '查看群成员', '群设置'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.clearChatHistory()
              break
            case 1:
              this.showGroupMembers()
              break
            case 2:
              this.showGroupSettings()
              break
          }
        }
      })
    },

    /**
     * 初始化MQTT连接
     */
    initMqttConnection() {
      // 设置MQTT库
      mqttClient.setMqttLib(mqtt)
      // 创建用户信息
      const userInfo = createUserInfo(
        store.state.app.userInfo.userId,
        store.state.app.userInfo.nickname || '用户',
        store.state.app.userInfo.channelCode,
        store.state.app.token,
        store.state.app.userInfo.avatar,
        'DEV'
      )

      // 设置回调函数
      const callbacks = {
        onConnect: () => {
          console.log('MQTT连接成功')
          this.isOnline = true
        },
        onMessage: (topic, mqttMsg) => {
          this.handleMqttMessage(mqttMsg)
        },
        onReconnect: () => {
          console.log('MQTT重连中...')
          this.isOnline = false
        },
        onError: (error) => {
          console.error('MQTT连接错误:', error)
          this.isOnline = false
        },
        onEnd: () => {
          console.log('MQTT连接已断开')
          this.isOnline = false
        }
      }

      // 连接MQTT
      this.mqttClientInstance = mqttClient.connect(userInfo, callbacks)
    },

    /**
     * 处理MQTT消息
     */
    handleMqttMessage(mqttMsg) {
      if (mqttMsg.command === MESSAGE_TYPES.CHAT_MSG) {
        const chatMsg = mqttMsg.data

        // 设置用户昵称
        if (this.userMap.hasOwnProperty(chatMsg.userId)) {
          chatMsg.nickname = this.userMap[chatMsg.userId].nickname
        }

        // 如果是当前群组的消息
        if (this.groupId === chatMsg.groupId) {
          this.list.push(chatMsg)
          this.scrollToBottom()

          // 发送已读回执
          this.sendReadReceipt(chatMsg)
        } else {
          // 其他群组的消息通知
        }
      }
    },

    /**
     * 发送已读回执
     */
    sendReadReceipt(message) {
      if (message.id) {
        const readTopic = `/chat/server/${this.selfUserId}/read`
        const readData = {
          id: message.id,
          groupId: this.groupId
        }
        mqttClient.publish(readTopic, readData)
      }
    },

    // ==================== 消息相关方法 ====================

    /**
     * 格式化消息列表，添加时间显示逻辑
     */
    formatMessages(messages, thresholdInMinutes = 10) {
      const formattedMessages = []
      let lastTimestamp = null

      messages.forEach((message) => {
        const currentTimestamp = new Date(message.createTime).getTime()
        if (!lastTimestamp || (currentTimestamp - lastTimestamp) / (1000 * 60) > thresholdInMinutes) {
          formattedMessages.push({
            ...message,
            showCreateTime: true
          })
          lastTimestamp = currentTimestamp
        } else {
          formattedMessages.push({
            ...message,
            showCreateTime: false
          })
        }
      })

      return formattedMessages
    },

    /**
     * 判断是否为自己发送的消息
     */
    isSelfMessage(message) {
      return message.userId === this.selfUserId
    },

    /**
     * 获取消息样式类名
     */
    getMessageClass(message) {
      return this.isSelfMessage(message) ? 'self-message' : 'friend-message'
    },

    /**
     * 获取用户头像
     */
    getUserAvatar(userId) {
      return this.userMap[userId]?.avatar || '/static/default-avatar.png'
    },

    /**
     * 获取用户昵称
     */
    getUserNickname(userId) {
      return this.userMap[userId]?.nickname || '未知用户'
    },

    /**
     * 是否显示昵称
     */
    showNickname(message, index) {
      // 群聊中显示昵称，私聊中不显示
      // 连续的同一用户消息只显示第一条的昵称
      if (index === 0) return true

      const prevMessage = this.messageList[index - 1]
      return prevMessage.userId !== message.userId
    },

    /**
     * 格式化时间显示
     */
    formatTime(timestamp) {
      const date = new Date(timestamp)
      const now = new Date()
      const diff = now.getTime() - date.getTime()

      // 今天
      if (diff < 24 * 60 * 60 * 1000 && now.getDate() === date.getDate()) {
        return date.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit'
        })
      }

      // 昨天
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)
      if (yesterday.getDate() === date.getDate()) {
        return (
          '昨天 ' +
          date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
          })
        )
      }

      // 更早
      return (
        date.toLocaleDateString('zh-CN') +
        ' ' +
        date.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit'
        })
      )
    },

    // ==================== 输入相关方法 ====================

    /**
     * 切换到语音模式
     */
    switchToVoiceMode() {
      this.inputMode = 'voice'
      this.inputFocus = false
    },

    /**
     * 切换到文本模式
     */
    switchToTextMode() {
      this.inputMode = 'text'
      this.inputFocus = true
    },

    /**
     * 处理输入框焦点
     */
    handleInputFocus() {
      this.inputFocus = true
      this.showEmojiPanel = false
      this.showMorePanel = false

      // 延迟滚动到底部，等待键盘弹出
      setTimeout(() => {
        this.scrollToBottom()
      }, 300)
    },

    /**
     * 处理输入框失焦
     */
    handleInputBlur() {
      this.inputFocus = false
    },

    /**
     * 处理输入内容变化
     */
    handleInput(e) {
      this.inputText = e.detail.value
    },

    /**
     * 发送文本消息
     */
    sendTextMessage() {
      if (!this.inputText.trim()) {
        uni.showToast({
          title: '消息不能为空',
          icon: 'none'
        })
        return
      }

      const chatMsg = {
        content: this.inputText.trim(),
        userId: this.selfUserId,
        groupId: this.groupId,
        msgType: 'text',
        localMsgId: this.generateLocalMsgId(),
        status: 'sending'
      }

      // 添加到消息列表
      this.list.push(chatMsg)

      // 发送MQTT消息
      const topic = `/chat/server/${this.selfUserId}/msg`
      mqttClient.publish(topic, chatMsg)

      // 清空输入框
      this.inputText = ''
      this.content = '' // 兼容旧版本

      // 滚动到底部
      this.scrollToBottom()
    },

    /**
     * 生成本地消息ID
     */
    generateLocalMsgId() {
      return Date.now() + '_' + Math.random().toString(36).substring(2, 11)
    },

    /**
     * 切换表情面板
     */
    toggleEmojiPanel() {
      this.showEmojiPanel = !this.showEmojiPanel
      this.showMorePanel = false
      this.inputFocus = false
    },

    /**
     * 切换更多功能面板
     */
    toggleMorePanel() {
      this.showMorePanel = !this.showMorePanel
      this.showEmojiPanel = false
      this.inputFocus = false
    },

    /**
     * 滚动到底部
     */
    scrollToBottom() {
      this.$nextTick(() => {
        this.scrollTop = this.list.length * 1000
        this.top = this.list.length * 1000 // 兼容旧版本
      })
    },

    /**
     * 加载更多消息
     */
    loadMoreMessages() {
      if (this.list.length > 0) {
        this.loadData(this.list[0].id)
        setTimeout(() => {
          this.scrollTop = 100
          this.top = 100 // 兼容旧版本
        }, 1000)
      }
    },

    // ==================== 语音相关方法 ====================

    /**
     * 开始录音
     */
    startRecording() {
      this.isRecording = true
      this.recordStart = true // 兼容旧版本
      this.recordingText = '正在录音...'
      this.voiceButtonText = '松开 发送'

      // 调用录音权限请求
      this.recReq()
    },

    /**
     * 停止录音
     */
    stopRecording() {
      this.isRecording = false
      this.recordStart = false // 兼容旧版本
      this.voiceButtonText = '按住 说话'

      // 调用录音停止
      this.recStop()
    },

    /**
     * 取消录音
     */
    cancelRecording() {
      this.isRecording = false
      this.recordStart = false // 兼容旧版本
      this.voiceButtonText = '按住 说话'

      // 取消录音逻辑
      if (RecordApp.GetCurrentRecOrNull()) {
        RecordApp.Stop(
          () => {
            console.log('录音已取消')
          },
          () => {
            console.log('取消录音失败')
          }
        )
      }
    },

    // ==================== 消息交互方法 ====================

    /**
     * 处理消息点击
     */
    handleMessageClick(message) {
      if (message.msgType === 'voice') {
        this.playVoiceMessage(message)
      } else if (message.msgType === 'image') {
        this.previewImage(message.content)
      }
    },

    /**
     * 播放语音消息
     */
    playVoiceMessage(message) {
      // 停止当前播放的音频
      if (this.music) {
        this.music.stop()
      }

      // 设置播放状态
      this.$set(message, 'isPlaying', true)

      // 创建音频播放器
      this.music = uni.createInnerAudioContext()
      this.music.src = message.content.audioSrc || message.content

      this.music.onPlay(() => {
        console.log('开始播放语音')
      })

      this.music.onEnded(() => {
        console.log('语音播放结束')
        this.$set(message, 'isPlaying', false)
        this.music = null
      })

      this.music.onError((error) => {
        console.error('语音播放错误:', error)
        this.$set(message, 'isPlaying', false)
        this.music = null
      })

      this.music.play()
    },

    /**
     * 预览图片
     */
    previewImage(imageUrl) {
      uni.previewImage({
        urls: [imageUrl],
        current: imageUrl
      })
    },

    /**
     * 显示消息菜单
     */
    showMessageMenu(message) {
      const items = ['复制']

      if (this.isSelfMessage(message)) {
        items.push('撤回')
      }

      uni.showActionSheet({
        itemList: items,
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.copyMessage(message)
              break
            case 1:
              if (this.isSelfMessage(message)) {
                this.withdrawMessage(message)
              }
              break
          }
        }
      })
    },

    /**
     * 复制消息
     */
    copyMessage(message) {
      if (message.msgType === 'text') {
        uni.setClipboardData({
          data: message.content,
          success: () => {
            uni.showToast({
              title: '已复制',
              icon: 'success'
            })
          }
        })
      }
    },

    /**
     * 撤回消息
     */
    withdrawMessage(message) {
      const withdrawTopic = `/chat/server/${this.selfUserId}/withdraw`
      const withdrawData = {
        id: message.id,
        localMsgId: message.localMsgId
      }

      mqttClient.publish(withdrawTopic, withdrawData)
    },

    /**
     * 显示用户资料
     */
    showUserProfile(userId) {
      console.log('显示用户资料:', userId)
      // 这里可以跳转到用户资料页面
    },

    /**
     * 获取语音图标
     */
    getVoiceIcon(message) {
      const isLeft = !this.isSelfMessage(message)
      return isLeft ? '/static/icon-voice-left.png' : '/static/icon-voice-right.png'
    },

    /**
     * 获取语音时长
     */
    getVoiceDuration(message) {
      if (typeof message.content === 'object' && message.content.msg) {
        return message.content.msg
      }
      return '1"'
    },

    // ==================== 多媒体相关方法 ====================

    /**
     * 选择图片
     */
    chooseImage() {
      uni.chooseImage({
        count: 1,
        sourceType: ['album'],
        success: (res) => {
          this.uploadImage(res.tempFilePaths[0])
        }
      })
    },

    /**
     * 拍照
     */
    takePhoto() {
      uni.chooseImage({
        count: 1,
        sourceType: ['camera'],
        success: (res) => {
          this.uploadImage(res.tempFilePaths[0])
        }
      })
    },

    /**
     * 选择视频
     */
    chooseVideo() {
      uni.chooseVideo({
        sourceType: ['album', 'camera'],
        success: (res) => {
          console.log('选择视频:', res)
          // 这里可以添加视频上传逻辑
        }
      })
    },

    /**
     * 选择文件
     */
    chooseFile() {
      // #ifdef H5
      const input = document.createElement('input')
      input.type = 'file'
      input.onchange = (e) => {
        const file = e.target.files[0]
        console.log('选择文件:', file)
        // 这里可以添加文件上传逻辑
      }
      input.click()
      // #endif

      // #ifndef H5
      uni.showToast({
        title: '该平台暂不支持文件选择',
        icon: 'none'
      })
      // #endif
    },

    // ==================== 群组管理方法 ====================

    /**
     * 清空聊天记录
     */
    clearChatHistory() {
      uni.showModal({
        title: '确认清空',
        content: '确定要清空聊天记录吗？',
        success: (res) => {
          if (res.confirm) {
            this.list = []
            uni.showToast({
              title: '已清空',
              icon: 'success'
            })
          }
        }
      })
    },

    /**
     * 显示群成员
     */
    showGroupMembers() {
      // 这里可以跳转到群成员页面
      uni.navigateTo({
        url: `/pages/groupMembers/groupMembers?groupId=${this.groupId}`
      })
    },

    /**
     * 显示群设置
     */
    showGroupSettings() {
      // 这里可以跳转到群设置页面
      uni.navigateTo({
        url: `/pages/groupSettings/groupSettings?groupId=${this.groupId}`
      })
    },

    /**
     * 处理通知点击
     */
    onClickChild(data) {
      console.log('点击消息通知:', data)
      // 可以在这里处理通知点击事件，比如跳转到对应的聊天
    },

    // ==================== 兼容旧版本的方法 ====================

    send() {
      if (this.content === '') {
        uni.showToast({
          title: '消息不能为空',
          icon: 'none'
        })
        return
      }

      let chatMsg = {
        content: this.content,
        userId: this.selfUserId,
        groupId: this.groupId,
        msgType: 'text'
      }
      this.list.push(chatMsg)

      this.mqttClient.publish('/chat/server/' + store.state.app.userInfo.userId + '/msg', JSON.stringify(chatMsg))
      //this.mqttClient.
      //推送消息到客户端
      this.content = ''
      this.scrollToBottom()
    },

    chooseImage() {
      let self = this
      uni.chooseImage({
        sourceType: ['camera', 'album'],
        success: (res) => {
          self.uploadImage(res.tempFilePaths[0])
        }
      })
    },

    scrollToBottom() {
      this.top = this.list.length * 1000
      console.log('🚀 ~ scrollToBottom ~ this.top:', this.top)
    },

    msgClick(data) {
      if (data.msgType === 'voice') {
        this.music = uni.createInnerAudioContext() //创建播放器对象
        this.music.src = data.content.audioSrc // static文件夹下的音频
        this.music.play() //执行播放
        this.music.onEnded(() => {
          //播放结束
          this.music = null
        })
      }
    },

    authTips() {
      uni.showModal({
        title: '提示',
        content: '您拒绝了麦克风权限，将导致功能不能正常使用，去设置权限？',
        confirmText: '去设置',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            uni.openSetting({
              success: (res) => {
                if (res.authSetting['scope.record']) {
                  console.log('已授权麦克风')
                  this._recordAuth = true
                } else {
                  // 未授权
                  wx.showModal({
                    title: '提示',
                    content: '您未授权麦克风，功能将无法使用',
                    showCancel: false,
                    confirmText: '知道了'
                  })
                }
              }
            })
          }
        }
      })
    },

    touchstart() {
      //开始录音
      const _permission = 'scope.record'
      uni.getSetting({
        success: (res) => {
          // 判断是否有相关权限属性
          if (res.authSetting.hasOwnProperty(_permission)) {
            // 属性存在，且为false，用户拒绝过权限
            if (!res.authSetting[_permission]) {
              this.authTips()
            } else {
              // 已授权
              this._recordAuth = true
              // 开始录音
              recorderManager.start()
              recorderManager.onStart(() => {
                this.recordStart = true
              })

              // 错误回调
              recorderManager.onError((res) => {
                console.log('recorder error', res)
                uni.showToast({
                  icon: 'none',
                  title: '系统出错，请重试'
                })
                this.recordStart = false
              })
            }
          } else {
            // 属性不存在，需要授权
            uni.authorize({
              scope: _permission,
              success: () => {
                // 授权成功
                this._recordAuth = true
              },
              fail: (res) => {
                /**
                 * 104 未授权隐私协议
                 * 用户可能拒绝官方隐私授权弹窗，为了避免过度弹窗打扰用户，开发者再次调用隐私相关接口时，
                 * 若距上次用户拒绝不足10秒，将不再触发弹窗，直接给到开发者用户拒绝隐私授权弹窗的报错
                 */
                if (res.errno === 104) {
                  uni.showModal({
                    title: '温馨提示',
                    content: '您拒绝了隐私协议，请稍后再试',
                    confirmText: '知道了',
                    showCancel: false,
                    success: () => {}
                  })
                } else {
                  // 用户拒绝授权
                  this.authTips()
                }
              }
            })
          }
        }
      })
    },

    touchend() {
      if (!this._recordAuth || !this.recordStart) return
      //停止录音
      recorderManager.stop()
      recorderManager.onStop((res) => {
        console.log('结束录音', res)
        const { duration, tempFilePath } = res
        this.recordStart = false

        this.list.push({
          content: `语音 ${Math.round(duration / 1000)}''`,
          audioSrc: tempFilePath,
          userType: 'self',
          avatar: this._selfAvatar,
          messageType: 'voice'
        })
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      })
    },

    //播放声音
    play(src) {
      this._innerAudioContext = wx.createInnerAudioContext()
      this._innerAudioContext.src = src
      this._innerAudioContext.play()
      this._innerAudioContext.onPlay(() => {
        console.log('开始播放')
      })
      this._innerAudioContext.onEnded(() => {
        // 播放完毕，清除音频链接
        console.log('播放完毕')
      })
      this._innerAudioContext.onError((res) => {
        console.log('audio play error', res)
      })
    },

    async loadData(idEnd = '') {
      try {
        if (this.loading) return
        if (this.loadend) return
        this.loading = true
        let data = {
          page: this.page,
          pageSize: this.pageSize,
          groupId: this.groupId
        }
        if (idEnd) {
          data.id_end = idEnd
        }
        const [res, err] = await msglist(data)
        if (res) {
          //this.users = res
          try {
            res.forEach((item) => {
              if (item.msgType === 'voice' && item.content.length > 10) {
                console.log('🚀 ~ loadData ~ item.msgType:', JSON.parse(item.content))
                item.content = JSON.parse(item.content)
              }
            })
          } catch (error) {
            console.log('🚀 ~ loadData ~ error:', error)
            //TODO handle the exception
          }
          this.list = SplitArray(res, this.list)
          this.loadend = res.length < this.pageSize
          console.log('🚀 ~  ~ res:', res)
          if (!idEnd) {
            setTimeout(() => {
              this.scrollToBottom()
            }, 100)
          }
        }
        if (err) {
          console.log('🚀 ~  ~ err:', err)
        }
      } catch (error) {
        //TODO handle the exception
      } finally {
        this.loading = false
      }
    },
    async connectMqtt() {
      var self = this
      let opts = {
        protocolId: 'MQTT',
        protocolVersion: 4,
        clientId: store.state.app.userInfo.userId,
        username: store.state.app.userInfo.channelCode,
        password: store.state.app.token,
        clean: false
      }
      self.mqttClient = mqtt.connect(store.state.app.userInfo.wsUrl, opts)
      self.mqttClient
        .on('connect', function () {
          //self.logs.push('on connect')
          clearInterval(self.mqttPingIntetval)
          self.mqttPingIntetval = setInterval(function () {
            self.mqttClient.publish('/chat/server/' + store.state.app.userInfo.userId + '/ping', '1')
            console.log('ping-2')
          }, 10000)
          self.mqttClient.subscribe('/chat/client/' + store.state.app.userInfo.userId, function (err) {
            if (!err) {
              // client.publish('/chat/server/' + store.state.app.userInfo.userId + '/msg',
              // 	'{"groupId":"1911787038405492737","msgType":"text","content":"亭亭白桦"}'
              // )
            }
          })
        })
        .on('reconnect', function () {
          //self.logs.push('on reconnect')
        })
        .on('error', function () {
          //self.logs.push('on error')
        })
        .on('end', function () {
          console.log('MQTT断开连接-2')
          //self.logs.push('on end')
        })
        .on('message', function (topic, message) {
          console.log('message.toString', message.toString())
          let mqttMsg = JSON.parse(message.toString())
          if (mqttMsg['command'] === 'chatMsg') {
            let chatMsg = mqttMsg['data']
            if (self.userMap.hasOwnProperty(chatMsg['userId'])) {
              chatMsg['nickname'] = self.userMap[chatMsg['userId']]['nickname']
            }
            if (self.groupId === chatMsg['groupId']) {
              self.list.push(chatMsg)
              //告诉服务器，已读
              self.mqttClient.publish('/chat/server/' + store.state.app.userInfo.userId + '/read', '{"groupId":' + self.groupId + '}')
            } else {
              // 消息通知
              try {
                console.log('🚀 ~ self:', self)
              } catch (error) {
                console.log('🚀 ~ error:', error)
                //TODO handle the exception
              }
            }
          }
        })
    },
    uploadImage(filePath) {
      uni.uploadFile({
        url: '/jeecg-boot/huyun/front/chat/upload', // 替换为你的服务器接口地址
        filePath: filePath, // 临时文件路径
        name: 'file', // 后端接收文件的字段名（需与服务器一致）
        formData: {},
        header: {
          'x-access-token': store.state.app.token // 自定义请求头
          // 注意：不要手动设置 Content-Type，uni-app 会自动处理
        },
        success: (res) => {
          // 上传成功，处理服务器响应
          if (res.statusCode === 200) {
            const data = JSON.parse(res.data) // 解析服务器返回的数据

            let chatMsg = {
              content: data.message,
              userId: this.selfUserId,
              groupId: this.groupId,
              msgType: 'image'
            }
            this.list.push(chatMsg)
            console.log('chatMsgImage', chatMsg)
            this.mqttClient.publish('/chat/server/' + store.state.app.userInfo.userId + '/msg', JSON.stringify(chatMsg))
            this.scrollToBottom()
            // uni.showToast({
            // 	title: '上传成功',
            // 	icon: 'success'
            // });
            console.log('服务器返回:', data)
          } else {
            uni.showToast({
              title: '上传失败',
              icon: 'none'
            })
          }
        },
        fail: (err) => {
          uni.showToast({
            title: '上传失败',
            icon: 'none'
          })
          console.error('上传失败:', err)
        }
      })
    },
    uploadFile(opt, successCallback, errorCallback) {
      uni.uploadFile({
        url: '/jeecg-boot/huyun/front/chat/upload', // 替换为你的服务器接口地址
        file: opt.file, // 临时文件路径
        name: 'file', // 后端接收文件的字段名（需与服务器一致）
        formData: {},
        header: {
          'x-access-token': store.state.app.token // 自定义请求头
          // 注意：不要手动设置 Content-Type，uni-app 会自动处理
        },
        success: (res) => {
          console.log('🚀 ~ uploadFile ~ res:', res)
          // 上传成功，处理服务器响应
          if (res.statusCode === 200) {
            const data = JSON.parse(res.data) // 解析服务器返回的数据
            successCallback && successCallback(data.message)
          } else {
            errorCallback && errorCallback(res)
          }
        },
        fail: (err) => {
          console.log('🚀 ~ uploadFile ~ err:', err)
          errorCallback && errorCallback(err)
        }
      })
    },
    handleScrolltoupper(e) {
      this.loadData(this.list[0].id)
      setTimeout(() => {
        this.top = 100
      }, 1000)
    },
    onClickChild(data) {
      console.log('点击消息回调函数', data)
    },
    handleText() {
      this.messageType = 'text'
      this.focus = true
    },
    onBlur() {
      this.focus = false
    },
    //请求录音权限
    recReq() {
      uni.startRecord
      //
      //编译成App时提供的授权许可（编译成H5、小程序为免费授权可不填写）；如果未填写授权许可，将会在App打开后第一次调用请求录音权限时，弹出“未获得商用授权时，App上仅供测试”提示框
      //RecordApp.UniAppUseLicense='我已获得UniAppID=*****的商用授权';

      RecordApp.RequestPermission_H5OpenSet = { audioTrackSet: { noiseSuppression: true, echoCancellation: true, autoGainControl: true } } //这个是Start中的audioTrackSet配置，在h5（H5、App+renderjs）中必须提前配置，因为h5中RequestPermission会直接打开录音

      RecordApp.UniWebViewActivate(this) //App环境下必须先切换成当前页面WebView
      RecordApp.RequestPermission(
        () => {
          console.log('已获得录音权限，可以开始录音了')
          this.recStart()
          this.recordStart = true
        },
        (msg, isUserNotAllow) => {
          if (isUserNotAllow) {
            //用户拒绝了录音权限
            //这里你应当编写代码进行引导用户给录音权限，不同平台分别进行编写
            //
          }
          console.error('请求录音权限失败：' + msg)
        }
      )
    },

    //开始录音
    recStart() {
      //Android App如果要后台录音，需要启用后台录音保活服务（iOS不需要），需使用配套原生插件、或使用第三方保活插件
      //RecordApp.UniNativeUtsPluginCallAsync("androidNotifyService",{ title:"正在录音" ,content:"正在录音中，请勿关闭App运行" }).then(()=>{...}).catch((e)=>{...}) 注意必须RecordApp.RequestPermission得到权限后调用
      //录音配置信息
      var set = {
        type: 'mp3',
        sampleRate: 16000,
        bitRate: 16, //mp3格式，指定采样率hz、比特率kbps，其他参数使用默认配置；注意：是数字的参数必须提供数字，不要用字符串；需要使用的type类型，需提前把格式支持文件加载进来，比如使用wav格式需要提前加载wav.js编码引擎
        /*,audioTrackSet:{ //可选，如果需要同时播放声音（比如语音通话），需要打开回声消除（并不一定会生效；打开后声音可能会从听筒播放，部分环境下（如小程序、App原生插件）可调用接口切换成扬声器外放）
	                //注意：H5、App+renderjs中需要在请求录音权限前进行相同配置RecordApp.RequestPermission_H5OpenSet后此配置才会生效
	                echoCancellation:true,noiseSuppression:true,autoGainControl:true} */
        onProcess: (buffers, powerLevel, duration, sampleRate, newBufferIdx, asyncEnd) => {
          //全平台通用：可实时上传（发送）数据，配合Recorder.SampleData方法，将buffers中的新数据连续的转换成pcm上传，或使用mock方法将新数据连续的转码成其他格式上传，可以参考Recorder文档里面的：Demo片段列表 -> 实时转码并上传-通用版；基于本功能可以做到：实时转发数据、实时保存数据、实时语音识别（ASR）等

          //注意：App里面是在renderjs中进行实际的音频格式编码操作，此处的buffers数据是renderjs实时转发过来的，修改此处的buffers数据不会改变renderjs中buffers，所以不会改变生成的音频文件，可在onProcess_renderjs中进行修改操作就没有此问题了；如需清理buffers内存，此处和onProcess_renderjs中均需要进行清理，H5、小程序中无此限制
          //注意：如果你要用只支持在浏览器中使用的Recorder扩展插件，App里面请在renderjs中引入此扩展插件，然后在onProcess_renderjs中调用这个插件；H5可直接在这里进行调用，小程序不支持这类插件；如果调用插件的逻辑比较复杂，建议封装成js文件，这样逻辑层、renderjs中直接import，不需要重复编写

          //H5、小程序等可视化图形绘制，直接运行在逻辑层；App里面需要在onProcess_renderjs中进行这些操作
          // #ifdef H5 || MP-WEIXIN
          if (this.waveView) this.waveView.input(buffers[buffers.length - 1], powerLevel, sampleRate)
          // #endif

          /*实时释放清理内存，用于支持长时间录音；在指定了有效的type时，编码器内部可能还会有其他缓冲，必须同时提供takeoffEncodeChunk才能清理内存，否则type需要提供unknown格式来阻止编码器内部缓冲，App的onProcess_renderjs中需要进行相同操作
	                if(this.clearBufferIdx>newBufferIdx){ this.clearBufferIdx=0 } //重新录音了就重置
	                for(var i=this.clearBufferIdx||0;i<newBufferIdx;i++) buffers[i]=null;
	                this.clearBufferIdx=newBufferIdx; */
        },
        onProcess_renderjs: `function(buffers,powerLevel,duration,sampleRate,newBufferIdx,asyncEnd){
	                //App中在这里修改buffers会改变生成的音频文件，但注意：buffers会先转发到逻辑层onProcess后才会调用本方法，因此在逻辑层的onProcess中需要重新修改一遍
	                //本方法可以返回true，renderjs中的onProcess将开启异步模式，处理完后调用asyncEnd结束异步，注意：这里异步修改的buffers一样的不会在逻辑层的onProcess中生效
	                //App中是在renderjs中进行的可视化图形绘制，因此需要写在这里，this是renderjs模块的this（也可以用This变量）；如果代码比较复杂，请直接在renderjs的methods里面放个方法xxxFunc，这里直接使用this.xxxFunc(args)进行调用
	                if(this.waveView) this.waveView.input(buffers[buffers.length-1],powerLevel,sampleRate);

	                /*和onProcess中一样进行释放清理内存，用于支持长时间录音
	                if(this.clearBufferIdx>newBufferIdx){ this.clearBufferIdx=0 } //重新录音了就重置
	                for(var i=this.clearBufferIdx||0;i<newBufferIdx;i++) buffers[i]=null;
	                this.clearBufferIdx=newBufferIdx; */
	            }`,
        onProcessBefore_renderjs: `function(buffers,powerLevel,duration,sampleRate,newBufferIdx){
	                //App中本方法会在逻辑层onProcess之前调用，因此修改的buffers会转发给逻辑层onProcess，本方法没有asyncEnd参数不支持异步处理
	                //一般无需提供本方法只用onProcess_renderjs就行，renderjs的onProcess内部调用过程：onProcessBefore_renderjs -> 转发给逻辑层onProcess -> onProcess_renderjs
	            }`,

        takeoffEncodeChunk: true
          ? null
          : (chunkBytes) => {
              //全平台通用：实时接收到编码器编码出来的音频片段数据，chunkBytes是Uint8Array二进制数据，可以实时上传（发送）出去
              //App中如果未配置RecordApp.UniWithoutAppRenderjs时，建议提供此回调，因为录音结束后会将整个录音文件从renderjs传回逻辑层，由于uni-app的逻辑层和renderjs层数据交互性能实在太拉跨了，大点的文件传输会比较慢，提供此回调后可避免Stop时产生超大数据回传
              //App中使用原生插件时，可方便的将数据实时保存到同一文件，第一帧时append:false新建文件，后面的append:true追加到文件
              //RecordApp.UniNativeUtsPluginCallAsync("writeFile",{path:"xxx.mp3",append:回调次数!=1, dataBase64:RecordApp.UniBtoa(chunkBytes.buffer)}).then(...).catch(...)
            },
        takeoffEncodeChunk_renderjs: true
          ? null
          : `function(chunkBytes){
	                //App中这里可以做一些仅在renderjs中才生效的事情，不提供也行，this是renderjs模块的this（也可以用This变量）
	            }`,

        start_renderjs: `function(){
	                //App中可以放一个函数，在Start成功时renderjs中会先调用这里的代码，this是renderjs模块的this（也可以用This变量）
	                //放一些仅在renderjs中才生效的事情，比如初始化，不提供也行
	            }`,
        stop_renderjs: `function(arrayBuffer,duration,mime){
	                //App中可以放一个函数，在Stop成功时renderjs中会先调用这里的代码，this是renderjs模块的this（也可以用This变量）
	                //放一些仅在renderjs中才生效的事情，不提供也行
	            }`
      }

      RecordApp.UniWebViewActivate(this) //App环境下必须先切换成当前页面WebView
      RecordApp.Start(
        set,
        () => {
          console.log('已开始录音')
          //【稳如老狗WDT】可选的，监控是否在正常录音有onProcess回调，如果长时间没有回调就代表录音不正常
          //var wdt=this.watchDogTimer=setInterval ... 请参考示例Demo的main_recTest.vue中的watchDogTimer实现

          //创建音频可视化图形绘制，App环境下是在renderjs中绘制，H5、小程序等是在逻辑层中绘制，因此需要提供两段相同的代码
          //view里面放一个canvas，canvas需要指定宽高（下面style里指定了300*100）
          //<canvas type="2d" class="recwave-WaveView" style="width:300px;height:100px"></canvas>
          RecordApp.UniFindCanvas(
            this,
            ['.recwave-WaveView'],
            `
	                this.waveView=Recorder.WaveView({compatibleCanvas:canvas1, width:300, height:100});
	            `,
            (canvas1) => {
              this.waveView = Recorder.WaveView({ compatibleCanvas: canvas1, width: 300, height: 100 })
            }
          )
        },
        (msg) => {
          console.error('开始录音失败：' + msg)
        }
      )
    },

    //暂停录音
    recPause() {
      if (RecordApp.GetCurrentRecOrNull()) {
        RecordApp.Pause()
        console.log('已暂停')
      }
    },
    //继续录音
    recResume() {
      if (RecordApp.GetCurrentRecOrNull()) {
        RecordApp.Resume()
        console.log('继续录音中...')
      }
    },

    //停止录音
    recStop() {
      //RecordApp.UniNativeUtsPluginCallAsync("androidNotifyService",{ close:true }) //关闭Android App后台录音保活服务
      this.recordStart = false
      RecordApp.Stop(
        (arrayBuffer, duration, mime) => {
          //全平台通用：arrayBuffer是音频文件二进制数据，可以保存成文件或者发送给服务器
          //App中如果在Start参数中提供了stop_renderjs，renderjs中的函数会比这个函数先执行

          //注意：当Start时提供了takeoffEncodeChunk后，你需要自行实时保存录音文件数据，因此Stop时返回的arrayBuffer的长度将为0字节

          //如果是H5环境，也可以直接构造成Blob/File文件对象，和Recorder使用一致
          // #ifdef H5
          var blob = new Blob([arrayBuffer], { type: mime })
          console.log(blob, (window.URL || webkitURL).createObjectURL(blob))
          var file = new File([arrayBuffer], 'recorder.mp3')
          //uni.uploadFile({file:file, ...}) //参考demo中的test_upload_saveFile.vue
          console.log('🚀 ~ recStop ~ this.duration:', duration)
          this.uploadFile({ file: file }, (res) => {
            console.log('🚀 ~ recStop ~ res:', res)
            const chatMsg = {
              content: {
                msg: `语音 ${Math.round(duration / 1000)}"`,
                audioSrc: res
              },
              userType: 'self',
              userId: this.selfUserId,
              groupId: this.groupId,
              msgType: 'voice'
            }
            this.list.push(chatMsg)
            this.mqttClient.publish('/chat/server/' + store.state.app.userInfo.userId + '/msg', JSON.stringify(chatMsg))
            this.$nextTick(() => {
              this.scrollToBottom()
            })
          })

          // #endif

          //如果是App、小程序环境，可以直接保存到本地文件，然后调用相关网络接口上传
          // #ifdef APP || MP-WEIXIN
          RecordApp.UniSaveLocalFile(
            'recorder.mp3',
            arrayBuffer,
            (savePath) => {
              console.log(savePath) //app保存的文件夹为`plus.io.PUBLIC_DOWNLOADS`，小程序为 `wx.env.USER_DATA_PATH` 路径
              //uni.uploadFile({filePath:savePath, ...}) //参考demo中的test_upload_saveFile.vue
            },
            (errMsg) => {
              console.error(errMsg)
            }
          )
          // #endif
        },
        (msg) => {
          console.error('结束录音失败：' + msg)
        }
      )
    }
  }
}
</script>

<style lang="scss" scoped>
/* ==================== 主容器样式 ==================== */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* ==================== 顶部导航栏 ==================== */
.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #e5e5e5;

  .header-left {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .back-icon {
      font-size: 40rpx;
      color: #007aff;
      font-weight: bold;
    }
  }

  .header-center {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;

    .chat-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }

    .online-status {
      font-size: 24rpx;
      color: #999;
      margin-top: 4rpx;
    }
  }

  .header-right {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .more-icon {
      font-size: 32rpx;
      color: #333;
      font-weight: bold;
    }
  }
}

/* ==================== 消息列表 ==================== */
.message-list {
  flex: 1;
  background-color: #f5f5f5;

  .load-more {
    display: flex;
    justify-content: center;
    padding: 20rpx;

    .loading-text {
      font-size: 28rpx;
      color: #999;
    }
  }

  .message-content {
    padding: 20rpx 30rpx 120rpx;
  }

  .bottom-placeholder {
    height: 20rpx;
  }
}

/* ==================== 时间分隔线 ==================== */
.time-divider {
  display: flex;
  justify-content: center;
  margin: 30rpx 0;

  .time-text {
    padding: 8rpx 20rpx;
    font-size: 24rpx;
    color: #999;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 20rpx;
  }
}

/* ==================== 消息项 ==================== */
.message-item {
  display: flex;
  margin-bottom: 30rpx;

  &.self-message {
    flex-direction: row-reverse;

    .message-body {
      align-items: flex-end;
    }

    .message-bubble {
      background-color: #95ec69;

      &::after {
        right: -12rpx;
        border-left-color: #95ec69;
        border-right: none;
      }
    }
  }

  &.friend-message {
    .message-bubble {
      background-color: #fff;

      &::after {
        left: -12rpx;
        border-right-color: #fff;
        border-left: none;
      }
    }
  }
}

/* ==================== 头像容器 ==================== */
.avatar-container {
  margin: 0 20rpx;

  .avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 8rpx;
  }
}

/* ==================== 消息主体 ==================== */
.message-body {
  display: flex;
  flex-direction: column;
  max-width: 60%;

  .nickname {
    font-size: 24rpx;
    color: #999;
    margin-bottom: 8rpx;
    padding: 0 20rpx;
  }
}

/* ==================== 消息气泡 ==================== */
.message-bubble {
  position: relative;
  padding: 20rpx;
  border-radius: 12rpx;
  word-wrap: break-word;
  word-break: break-all;

  &::after {
    content: '';
    position: absolute;
    top: 20rpx;
    width: 0;
    height: 0;
    border: 12rpx solid transparent;
  }

  .text-content {
    font-size: 32rpx;
    line-height: 1.4;
    color: #333;
  }

  .image-content {
    .message-image {
      max-width: 400rpx;
      max-height: 400rpx;
      border-radius: 8rpx;
    }
  }

  .voice-content {
    display: flex;
    align-items: center;
    min-width: 120rpx;

    &.playing {
      .voice-icon {
        animation: voice-wave 1s infinite;
      }
    }

    .voice-icon {
      width: 40rpx;
      height: 40rpx;
      margin-right: 16rpx;

      &.voice-animation {
        animation: voice-wave 1s infinite;
      }
    }

    .voice-duration {
      font-size: 28rpx;
      color: #333;
    }
  }

  .message-status {
    position: absolute;
    right: -40rpx;
    top: 50%;
    transform: translateY(-50%);

    .status-icon {
      font-size: 24rpx;
      color: #999;

      &.read {
        color: #07c160;
      }
    }
  }
}

/* ==================== 输入工具栏 ==================== */
.input-toolbar {
  position: fixed;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top: 1rpx solid #e5e5e5;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  z-index: 1000;
}

/* ==================== 语音模式 ==================== */
.voice-mode {
  .voice-controls {
    display: flex;
    align-items: center;
    gap: 20rpx;

    .mode-switch {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f5f5f5;
      border-radius: 8rpx;
      font-size: 28rpx;
      color: #333;
    }

    .voice-button {
      flex: 1;
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f5f5f5;
      border-radius: 8rpx;

      &.recording {
        background-color: #ff4757;
      }

      .voice-text {
        font-size: 32rpx;
        color: #333;

        .recording & {
          color: #fff;
        }
      }
    }
  }
}

/* ==================== 文本模式 ==================== */
.text-mode {
  .input-controls {
    display: flex;
    align-items: flex-end;
    gap: 20rpx;

    .voice-btn,
    .emoji-btn,
    .more-btn {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
      color: #666;
      background-color: #f5f5f5;
      border-radius: 8rpx;
    }

    .input-wrapper {
      flex: 1;

      .text-input {
        width: 100%;
        min-height: 60rpx;
        max-height: 120rpx;
        padding: 16rpx 20rpx;
        font-size: 32rpx;
        line-height: 1.4;
        background-color: #f5f5f5;
        border-radius: 8rpx;
        border: none;
        outline: none;
        resize: none;
      }
    }

    .send-btn {
      padding: 16rpx 32rpx;
      background-color: #07c160;
      border-radius: 8rpx;

      .send-text {
        font-size: 28rpx;
        color: #fff;
      }
    }
  }
}

/* ==================== 表情面板 ==================== */
.emoji-panel {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 500rpx;
  background-color: #fff;
  border-top: 1rpx solid #e5e5e5;
  z-index: 999;
}

/* ==================== 更多功能面板 ==================== */
.more-panel {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-top: 1rpx solid #e5e5e5;
  padding: 40rpx 30rpx;
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
  z-index: 999;

  .more-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 40rpx;

    .more-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16rpx;

      .more-icon {
        width: 120rpx;
        height: 120rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 60rpx;
        background-color: #f5f5f5;
        border-radius: 20rpx;
      }

      .more-text {
        font-size: 24rpx;
        color: #666;
      }
    }
  }
}

/* ==================== 录音动画覆盖层 ==================== */
.recording-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;

  .recording-animation {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30rpx;
    padding: 60rpx;
    background-color: rgba(0, 0, 0, 0.8);
    border-radius: 20rpx;

    .wave-container {
      display: flex;
      align-items: center;
      gap: 8rpx;

      .wave {
        width: 8rpx;
        height: 40rpx;
        background-color: #07c160;
        border-radius: 4rpx;
        animation: wave-animation 1s ease-in-out infinite alternate;
      }
    }

    .recording-text {
      font-size: 32rpx;
      color: #fff;
      font-weight: 600;
    }

    .recording-tip {
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.8);
    }
  }
}

/* ==================== 动画定义 ==================== */
@keyframes voice-wave {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

@keyframes wave-animation {
  0% {
    height: 20rpx;
  }
  100% {
    height: 80rpx;
  }
}

/* ==================== 兼容旧版本样式 ==================== */
.message {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;

  .avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 10rpx;
    margin-right: 30rpx;
    margin-top: 20rpx;
  }

  .content {
    min-height: 80rpx;
    max-width: 60vw;
    box-sizing: border-box;
    font-size: 28rpx;
    line-height: 1.3;
    padding: 20rpx;
    border-radius: 10rpx;
    background: #fff;

    image {
      width: 200rpx;
      height: 200rpx;
    }
  }

  &.self {
    justify-content: flex-end;

    .avatar {
      margin: 0 0 0 30rpx;
    }
    .msg-box {
      display: flex;
      align-items: flex-start;
    }

    .content {
      position: relative;

      &::after {
        position: absolute;
        content: '';
        width: 0;
        height: 0;
        border: 16rpx solid transparent;
        border-left: 16rpx solid #fff;
        right: -28rpx;
        top: 24rpx;
      }
    }
  }

  &.friend {
    .content {
      position: relative;
      word-wrap: break-word;
      word-break: break-all;

      &::after {
        position: absolute;
        content: '';
        width: 0;
        height: 0;
        border: 16rpx solid transparent;
        border-right: 16rpx solid #fff;
        left: -28rpx;
        top: 24rpx;
      }
    }
  }
}

.tool {
  position: fixed;
  width: 100%;
  min-height: 120rpx;
  left: 0;
  bottom: 0;
  background: #fff;
  display: flex;
  // align-items: flex-start;
  align-items: center;
  box-sizing: border-box;
  padding: 20rpx 24rpx 20rpx 24rpx;
  padding-bottom: calc(20rpx + constant(safe-area-inset-bottom) / 2) !important;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom) / 2) !important;

  .left-icon {
    width: 56rpx;
    height: 56rpx;
    margin-right: 10rpx;
  }

  .input,
  .voice-crl {
    background: #eee;
    border-radius: 10rpx;
    height: 70rpx;
    margin-right: 16rpx;
    flex: 1;
    padding: 0 20rpx;
    box-sizing: border-box;
    font-size: 28rpx;
  }

  .thumb {
    width: 64rpx;
    height: 64rpx;
  }

  .voice-crl {
    text-align: center;
    line-height: 70rpx;
    font-weight: bold;
  }
}
.send-btn {
  width: 0;
  height: 56rpx;
  background-color: #00c75a;
  color: #fff;
  padding: 0;
  font-size: 28rpx;
  border-radius: 12rpx;
  line-height: 56rpx;
  margin-left: 12rpx;
  transition: all 0.3s ease-in;
}
.show-send-btn {
  width: 100rpx !important;
}

.audio-animation {
  position: fixed;
  // width: 100vw;
  // height: 100vh;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 202410;
  display: flex;
  justify-content: center;
  align-items: center;

  .text {
    text-align: center;
    font-size: 28rpx;
    color: #333;
    margin-top: 60rpx;
  }

  .audio-wave {
    padding: 50rpx;

    .audio-wave-text {
      background-color: blue;
      width: 7rpx;
      height: 12rpx;
      margin: 0 6rpx;
      border-radius: 5rpx;
      display: inline-block;
      border: none;
      animation: wave 0.25s ease-in-out;
      animation-iteration-count: infinite;
      animation-direction: alternate;
    }

    /*  声波动画  */
    @keyframes wave {
      from {
        transform: scaleY(1);
      }

      to {
        transform: scaleY(4);
      }
    }
  }
}
.nickname {
  font-size: 24rpx;
  margin-bottom: 6rpx;
  color: #a6a6a6;
}
.right-name {
  display: flex;
  align-items: center;
}
.time {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  .time-text {
    padding: 10rpx;
    font-size: 24rpx;
    color: #333;
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 8rpx;
  }
}
.icon-voice {
  width: 30rpx !important;
  height: 30rpx;
  margin-left: 12rpx;
}
.rate-90 {
  transform: rotate(180deg);
  margin-right: 12rpx;
}
.item-center {
  display: flex;
  align-items: center;
}
</style>
