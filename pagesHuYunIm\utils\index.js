/**
 * 对Promise进行封装，使其返回一个包含数据和错误信息的数组。
 * 这样做的目的是为了统一Promise的成功和失败的处理方式，使得调用者可以通过检查数组的特定索引来获取操作的结果或错误。
 *
 * @param {Promise<any>} promise 要处理的Promise对象。
 * @returns {Promise<[any, Error | undefined]>} 返回一个Promise，该Promise解析为一个数组，第一个元素是Promise成功时的数据，第二个元素是Promise失败时的错误对象，如果Promise成功，则第二个元素为undefined。
 */
export const pretty = function(promise) {
	return promise
		.then((data) => {
			// 当Promise成功解析时，返回一个包含数据和undefined的数组。
			return [data, undefined]
		})
		.catch((err) => {
			// 当Promise被拒绝时，返回一个包含undefined和错误对象的数组。
			return [undefined, err]
		})
}


/*
 * 合并数组
 */
export function SplitArray(list, sp) {
	if (typeof list != 'object') return [];
	if (sp === undefined) sp = [];
	for (var i = 0; i < list.length; i++) {
		sp.push(list[i]);
	}
	return sp;
}