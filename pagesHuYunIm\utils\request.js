import { HTTP_REQUEST_URL, HEADER, TOKENNAME, TIMEOUT } from '../config/api'
import store from '../store'
console.log('🚀 ~ store:', store)
/**
 * 发送请求
 */
function baseRequest(url, method, data, { noAuth = false, noVerify = false }) {
  let Url = HTTP_REQUEST_URL,
    header = HEADER
  if (store.state.token) header[TOKENNAME] = store.state.token
  return new Promise((reslove, reject) => {
    uni.request({
      // url: Url + '/huyun' + url,
      url: Url + url,
      method: method || 'GET',
      header: header,
      data: data || {},
      timeout: TIMEOUT,
      success: (res) => {
        console.log('res', res)
        if (noVerify) reslove(res.data, res)
        else if (res.data.code == 200) reslove(res.data.result, res)
        else if ([110002, 110003, 110004].indexOf(res.data.status) !== -1) {
          // toLogin();
          reject(res.data)
        } else if (res.data.status == 100103) {
          uni.showModal({
            title: `提示`,
            content: res.data.msg,
            showCancel: false,
            confirmText: '我知道了'
          })
        } else reject(res.data.msg || `系统错误`)
      },
      fail: (msg) => {
        let data = {
          mag: '请求失败',
          status: 1 //1没网
        }
        // #ifdef APP-PLUS
        reject(data)
        // #endif
        // #ifndef APP-PLUS
        reject('请求失败')
        // #endif
      }
    })
  })
}

const request = {}

;['options', 'get', 'post', 'put', 'head', 'delete', 'trace', 'connect'].forEach((method) => {
  request[method] = (api, data, opt) => baseRequest(api, method, data, opt || {})
})

export default request
