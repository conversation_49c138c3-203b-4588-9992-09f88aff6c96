{"version": 3, "sources": ["webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/index.vue?d1ac", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/index.vue?bd71", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/index.vue?ee6b", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/index.vue?6ab7", "uni-app:///pagesGoEasy/chat_page/components/item/index.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/index.vue?0131", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/index.vue?7a6a"], "names": ["components", "mText", "mImage", "mVideo", "mAudio", "mRedPacket", "mMap", "mEmojiImg", "mArticle", "mShareSbcf", "mShareMall", "mFunctionalModule", "props", "myid", "type", "default", "isMy", "item", "index", "data", "userInforData", "created", "methods", "imgLoad", "onClick", "longpressAvatar", "isLongpressAvatar", "onItem", "console", "member_id", "group_id", "longpress", "isLongpress", "query", "select", "boundingClientRect", "exec"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACiM;AACjM,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAwvB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACuF5wB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcA;AACA;AAAA,eACA;EACAA;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;EACA;EACAI;IACA;MACAC;IACA;EACA;EACAC;EACAC;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACAC;MACA;IACA;IACAC;MACA;MACAC;MACA;QAAAC;QAAAC;MAAA;IACA;IAEA;IACAC;MAAA;MACAC;MACA;MACAJ;MACA;QACA;QACA;QACA;QACA;;QAEA;QACAK,MACAC,0CACAC;UACA;QACA,GACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtLA;AAAA;AAAA;AAAA;AAAm7C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACAv8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesHuYunIm/pages/chat/components/item/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=52f9fea2&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=52f9fea2&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"52f9fea2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesHuYunIm/pages/chat/components/item/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=52f9fea2&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view>\n\t\t<!-- 群公告 -->\n\t\t<view class=\"text_26 icon_ group_notice\" v-if=\"item.type === 'group_notice'\">\n\t\t\t管理员设置了新的\n\t\t\t<text style=\"color: #fe6702; margin: 0 10rpx\">群公告</text>\n\t\t\t请及时查看\n\t\t</view>\n\t\t<view class=\"text_26 icon_ group_notice\" v-else-if=\"item.type === 'update_group_name'\">\n\t\t\t管理员修改了群名称为:\"\n\t\t\t<text style=\"color: #fe6702; margin: 0 10rpx\">{{ item.payload.name }}</text>\n\t\t\t\"\n\t\t</view>\n\t\t<view class=\"flex_r item\" :class=\"{ item_: isMy }\" v-else>\n\t\t\t<view class=\"item-img\" @longpress.stop=\"longpressAvatar\" @click.stop=\"onItem\">\n\t\t\t\t<!-- <view class=\"item-img-pendant\">\n\t\t\t\t\t<image class=\"img\" :src=\"item.senderData.avatar_pendant\" mode=\"aspectFill\"></image>\n\t\t\t\t</view> -->\n\t\t\t\t<view class=\"z_index2 item-img-url\">\n\t\t\t\t\t<image class=\"img\" :src=\"item.senderData.avatar\" mode=\"aspectFill\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view style=\"width: 22rpx\"></view>\n\t\t\t<view class=\"item-content\" :class=\"{ item_content: !isMy, item_content2: !item.senderData.name }\">\n\t\t\t\t<view class=\"text_24 color__ item-name\" v-if=\"item.senderData.name\">{{ item.senderData.name }}</view>\n\t\t\t\t<view class=\"flex_r fa_c item-content-box\" :class=\"{ row_reverse: isMy }\">\n\t\t\t\t\t<view class=\"flex_r fa_c item-content-box-box\" :class=\"'A' + item.timestamp\" @longpress.stop=\"longpress\">\n\t\t\t\t\t\t<view v-if=\"isMy\">\n\t\t\t\t\t\t\t<view class=\"loading\" v-if=\"item.status === 'new' || item.status === 'sending'\">\n\t\t\t\t\t\t\t\t<m-loading-icon iconColor=\"#989898\"></m-loading-icon>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"loading\" v-if=\"item.status === 'error'\">\n\t\t\t\t\t\t\t\t<image\n\t\t\t\t\t\t\t\t\tclass=\"img\"\n\t\t\t\t\t\t\t\t\tsrc=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTUxMiAzN2MyNjIuMzEgMCA0NzUgMjEyLjY5IDQ3NSA0NzVTNzc0LjMxIDk4NyA1MTIgOTg3IDM3IDc3NC4zMSAzNyA1MTIgMjQ5LjY5IDM3IDUxMiAzN3ptMCA2NjEuMDhjLTI5LjI4IDAtNTMuMDEgMjMuNzMtNTMuMDEgNTMuMDEgMCAyOS4yOCAyMy43MyA1My4wMSA1My4wMSA1My4wMSAyOS4yOCAwIDUzLjAxLTIzLjczIDUzLjAxLTUzLjAxIDAtMjkuMjgtMjMuNzMtNTMuMDEtNTMuMDEtNTMuMDF6bTAtNDc4LjE4Yy0zNy40MyAwLTY3Ljg2IDMwLjQzLTY3Ljg2IDY3Ljk2bDI1LjQ1IDMxOS43OC41OSA2LjEyYzMuMzkgMTkuOTggMjAuODEgMzUuMjMgNDEuODIgMzUuMjMgMjMuMTEgMCA0MS43Ny0xOC40NSA0Mi40MS00MS4zNWwyNS40NS0zMTkuNzgtLjQtNy40MWMtMy42OS0zNC4wNS0zMi41My02MC41NS02Ny40Ni02MC41NXoiIGZpbGw9IiNmNDM0MmYiIGRhdGEtc3BtLWFuY2hvci1pZD0iYTMxM3guc2VhcmNoX2luZGV4LjAuaTkuNjJlMTNhODF0ejVJRE4iIGNsYXNzPSJzZWxlY3RlZCIvPjwvc3ZnPg==\"\n\t\t\t\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t\t\t\t></image>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view v-if=\"item.type === 'text' || item.type === 'text_quote'\">\n\t\t\t\t\t\t\t<m-text :isMy=\"isMy\" :value=\"item\" @onClick=\"onClick\"></m-text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view v-else-if=\"item.type === 'image' || item.type === 'image_transmit'\" @tap.stop=\"onClick\">\n\t\t\t\t\t\t\t<m-image :isMy=\"isMy\" :value=\"item\" @imgLoad=\"imgLoad\"></m-image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view v-else-if=\"item.type === 'video'\" @tap.stop=\"onClick\">\n\t\t\t\t\t\t\t<m-video :isMy=\"isMy\" :value=\"item\"></m-video>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view v-else-if=\"item.type === 'audio'\">\n\t\t\t\t\t\t\t<m-audio :isMy=\"isMy\" :value=\"item\" @onClick=\"onClick\"></m-audio>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- 红包 -->\n\t\t\t\t\t\t<view v-else-if=\"item.type === 'red_envelope'\">\n\t\t\t\t\t\t\t<m-red-packet :isMy=\"isMy\" :myid=\"myid\" :value=\"item\" @onClick=\"onClick\"></m-red-packet>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- 表情包 -->\n\t\t\t\t\t\t<view v-else-if=\"item.type === 'emoji_pack'\" @tap.stop=\"onClick\">\n\t\t\t\t\t\t\t<m-emoji-img :isMy=\"isMy\" :value=\"item\"></m-emoji-img>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- 位置 -->\n\t\t\t\t\t\t<view v-else-if=\"item.type === 'map'\">\n\t\t\t\t\t\t\t<m-map :isMy=\"isMy\" :value=\"item\" @onClick=\"onClick\"></m-map>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- 文章 -->\n\t\t\t\t\t\t<view v-else-if=\"item.type === 'article'\">\n\t\t\t\t\t\t\t<m-article :isMy=\"isMy\" :value=\"item\" @onClick=\"onClick\"></m-article>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- 商家分享 -->\n\t\t\t\t\t\t<view v-else-if=\"item.type === 'share_SBCF'\">\n\t\t\t\t\t\t\t<m-share-sbcf :isMy=\"isMy\" :value=\"item\" @onClick=\"onClick\"></m-share-sbcf>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view v-else-if=\"item.type === 'share_mall'\">\n\t\t\t\t\t\t\t<m-share-mall :isMy=\"isMy\" :value=\"item\" @onClick=\"onClick\"></m-share-mall>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view v-else-if=\"item.type === 'functional_module'\">\n\t\t\t\t\t\t\t<m-functional-module :isMy=\"isMy\" :value=\"item\" @onClick=\"onClick\"></m-functional-module>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<view class=\"flex1\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport { to, vibrateShort } from '@/utils/index.js';\nimport mText from './m-text.vue';\nimport mImage from './m-image.vue';\nimport mVideo from './m-video.vue';\nimport mAudio from './m-audio.vue';\nimport mRedPacket from './m-redPacket.vue';\nimport mMap from './m-map.vue';\nimport mEmojiImg from './m-emoji-img.vue';\nimport mArticle from './m-article.vue';\nimport mShareSbcf from './m-share-sbcf.vue';\nimport mShareMall from './m-share-mall.vue';\n\nimport mFunctionalModule from './m-functional-module.vue';\n\nlet isLongpress = false;\nlet isLongpressAvatar = false;\nexport default {\n\tcomponents: {\n\t\tmText,\n\t\tmImage,\n\t\tmVideo,\n\t\tmAudio,\n\t\tmRedPacket,\n\t\tmMap,\n\t\tmEmojiImg,\n\t\tmArticle,\n\t\tmShareSbcf,\n\t\tmShareMall,\n\t\tmFunctionalModule\n\t},\n\tprops: {\n\t\tmyid: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: null\n\t\t},\n\t\tisMy: {\n\t\t\ttype: [Boolean, Number],\n\t\t\tdefault: false\n\t\t},\n\t\titem: {\n\t\t\ttype: Object,\n\t\t\tdefault: {}\n\t\t},\n\t\tindex: {\n\t\t\ttype: Number,\n\t\t\tdefault: 0\n\t\t}\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tuserInforData: {}\n\t\t};\n\t},\n\tcreated() {},\n\tmethods: {\n\t\timgLoad(e){\n\t\t\tthis.$emit('imgLoad', e);\n\t\t},\n\t\tonClick() {\n\t\t\tif (isLongpress) return (isLongpress = false);\n\t\t\tif (this.item.status === 'error') return;\n\t\t\tthis.$emit('onClick', this.item, this.index);\n\t\t},\n\t\t// @某人\n\t\tlongpressAvatar() {\n\t\t\tisLongpressAvatar = true;\n\t\t\tthis.$emit('mention', this.item, this.index);\n\t\t},\n\t\tonItem() {\n\t\t\tif (isLongpressAvatar) return (isLongpressAvatar = false);\n\t\t\tconsole.log(this.item);\n\t\t\tto('/pagesGoEasy/group_member_infor/index', { member_id: this.item.senderId, group_id: this.item.groupId });\n\t\t},\n\n\t\t// 长按\n\t\tlongpress(e) {\n\t\t\tisLongpress = true;\n\t\t\tvibrateShort();\n\t\t\tconsole.log(this.item);\n\t\t\tthis.$nextTick(() => {\n\t\t\t\t// let view = uni.createSelectorQuery().select(`.A${this.item.timestamp}`);\n\t\t\t\t// view.boundingClientRect((data) => {\n\t\t\t\t// \tthis.$emit('onLongpress', this.item, data);\n\t\t\t\t// }).exec();\n\n\t\t\t\tconst query = uni.createSelectorQuery().in(this);\n\t\t\t\tquery\n\t\t\t\t\t.select(`.A${this.item.timestamp}`)\n\t\t\t\t\t.boundingClientRect((data) => {\n\t\t\t\t\t\tthis.$emit('onLongpress', this.item, data);\n\t\t\t\t\t})\n\t\t\t\t\t.exec();\n\t\t\t});\n\t\t}\n\t}\n};\n</script>\n\n<style scoped lang=\"scss\">\n.group_notice {\n\twidth: 100%;\n\theight: 80rpx;\n\tcolor: #a3a3a3;\n}\n.row_reverse {\n\tflex-direction: row-reverse;\n}\n.item {\n\tbox-sizing: border-box;\n\tpadding-bottom: 20rpx;\n\tposition: relative;\n\tz-index: 0;\n\twidth: calc(100% - 50rpx);\n\tmargin: 0 auto;\n\t.item-name {\n\t\tmargin-bottom: 4rpx;\n\t}\n}\n.item_ {\n\tflex-direction: row-reverse;\n\t.item-name {\n\t\tposition: relative;\n\t\ttop: 0rpx;\n\t\tdisplay: none;\n\t}\n}\n.item-img {\n\tposition: relative;\n\twidth: 76rpx;\n\theight: 76rpx;\n\t.item-img-pendant {\n\t\tposition: absolute;\n\t\tz-index: 3;\n\t\ttop: -2rpx;\n\t\tleft: -2rpx;\n\t\tright: -2rpx;\n\t\tbottom: -2rpx;\n\t}\n\t.item-img-url {\n\t\twidth: 76rpx;\n\t\theight: 76rpx;\n\t\toverflow: hidden;\n\t\tborder-radius: 6rpx;\n\t\tbackground-color: #fff;\n\t}\n}\n.item-content {\n\twidth: calc(100% - 164rpx);\n\t.item-content-box {\n\t\tposition: relative;\n\t\t.item-content-box-box {\n\t\t}\n\t\t.loading {\n\t\t\twidth: 40rpx;\n\t\t\theight: 40rpx;\n\t\t\tmargin: 0 10rpx;\n\t\t}\n\t}\n}\n.item_content {\n\tposition: relative;\n\ttop: -10rpx;\n}\n.item_content2 {\n\tposition: relative;\n\ttop: 0rpx !important;\n}\n</style>\n", "import mod from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=52f9fea2&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=52f9fea2&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754983153543\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}