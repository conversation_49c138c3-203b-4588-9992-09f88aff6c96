{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/index.vue?cd8e", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/index.vue?8f05", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/index.vue?7a6e", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/index.vue?232c", "uni-app:///pagesGoEasy/chat_page/index.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/index.vue?0d9c", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesGoEasy/chat_page/index.vue?84cf"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "navigation", "bottomOperation", "item", "videoPlayerRef", "openRedPacket", "operate", "name", "data", "isHistoryGet", "reserveHeight", "keyboardheightchangeValue", "myid", "scroll_top", "userList", "groupCount", "pagueObj", "to", "history", "messages", "allLoaded", "videoPlayer", "show", "url", "context", "onLoad", "imageList", "envelopeClickList", "lastMessageTimeStamp", "groupId", "userInforMap", "自己的信息", "view", "bottomOperationRefHeight", "onPageScroll", "onReady", "computed", "page_font_size", "renderMessageDate", "message", "isSelf", "member_id", "envelope_top_opened", "methods", "setHeight", "reserveHeightRef", "getHeight", "view2", "boundingClientRect", "setTimeout", "exec", "imgLoad", "keyboardheightchange", "onPage", "touchmove", "onBottom", "focus", "loadHistoryMessage", "uni", "list", "onMessageReceived", "sendMessage", "markGroupMessageAsRead", "initMessageItem", "setEnvelopeClickList", "im", "pushList", "console", "onSetText", "onSetRedEnvelope", "bottomOperationScrollToBottom", "isBottomOperationScrollToBottom", "scrollToBottom", "onItem", "address", "latitude", "longitude", "id", "goods_id", "renewItem", "onLongpress", "quote", "thank", "transmit", "recalledEdit", "mention", "playVideo", "direction", "onVideoFullScreenChange", "playAudio", "innerAudioContext", "audioItem", "scroll", "scrolltolower", "scrolltoupper"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACwL;AACxL,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wRAEN;AACP,KAAK;AACL;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpEA;AAAA;AAAA;AAAA;AAA2sB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACkJ/tB;AAOA;AAmBA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAjBA;AAEA;AAEA;AACA;AACA;AACA;AAEA;;AAEA;AACA;;AAEA;AACA;AAIA;AACA;AACA;AACA;AACA;AAAA,eACA;EACAC;IACA;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;QACAT;MACA;MACAU;MACA;MACAC;QACAC;QACAC;MACA;MACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACAZ;cACA;cACAa;cACAC;cACAC;cACA;cACAC;cACA;cACA;gBACAC;gBACAA;cACA;cAAA,mBACAC;cACA;cACA;cACA;gBACA;gBACAC;kBACAC;gBACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;IAAA;IACA;IACAC;MAAA;MACA;QAAA;QACA,IACAC,6KACA,eACA;UACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;QACA;UAAAC;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;MACA;IACA;EACA;EAEAC;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC;IACA;IAEAC;MAAA;MACA;QACA;QACAd;UACA;UACA;YACA;cACA;cACAe,MACAC;gBACA;gBACA;kBACAC;oBACA,uBACA;kBACA;gBACA;cACA,GACAC;YACA;UACA;YACA;YACA;cACAD;gBACA,uBACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAE;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA;MACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IACAC;MACA;IAAA,CACA;IACAC;MACA;IACA;IACA;IACAC;MACA;QACA;QACA5B;QACA;QACA;MACA;IACA;IACA;IACA6B;MAAA;MACAC;MACA;MACAC;MACA;MACAA;QACA;QACA;UACAjC;QACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;MACA;IACA;IACAkC;MACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;QACA;QACAH;MACA;IACA;IACA;IACAI;MACA;IAAA,CACA;IACA;IACAC;MACAxB;MACA;MACA;QACAA;MACA;MACA;MACA;QACAA;QACAA;QACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAyB;MAAA;MACA;QACAC;MACA;QACAA;QACA;UACA;YACA;YACAP;UACA;QACA;MACA;IACA;IACA;IACAQ;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBACA;kBACAC;kBACA;gBACA;gBACA;gBACA;kBACAA;kBACA;gBACA;gBACA;gBACA;gBAEA;kBACA;gBACA;;gBAEA;gBACA;kBACA;gBACA;gBACA;gBACA;kBACA;gBACA;;gBAEA;gBACA;kBACAzC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACA0C;MAAA;MACA;MACA;QACA;UACA;UACAV;QACA;MACA;IACA;IACA;IACAW;MAAA;MACA;QACA;QACAX;MACA;IACA;IACAY;MAAA;MACAC;MACAtB;QACAsB;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACAN;MACA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;QACA;UACA;UACA;UACA;UACA;QACA;UACA;UACA;YACA;UACA;UACAT;UACAA;UACAvD;UACA;UACA;QACA;UACA;YACAI;YACAmE;YACAC;YACAC;UACA;UACA;QACA;UACA;UACA;QACA;UACA;YACAC;UACA;UACA;QACA;UACA;YACAC;UACA;UACA;QACA;UACA;UACA;QACA;UACA;MAAA;IAEA;IACA;IACAC;MACA;QACA;QACA5E;MACA;QACAA;MACA;MACA;MACA;QACA;UACA,sDACAA,MACA;UACA;QACA;MACA;IACA;IACA;IACA;IACA6E;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACA;UACAC;QACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;QACA;MACA;IACA;IACA;IACA;IACAC;MACA;QAAA;QACA;QACA;UACA;YACA;YACA;YACAC;YACAA;YACAvF;YACAwF;UACA;YACA;YACA;YACAD;UACA;UACA;QACA;QAEAC;QACAA;QACA;UACA;YACAD;YACAA;YACAA;UACA;QACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;UACA;UACAvF;UACAwF;QACA;QACAD;UACA;UACAvF;UACAwF;QACA;QACAD;UACAvB;QACA;MACA;IACA;IACA;IAEA;IACAyB;MACA/E;MACA;MACA;MACA;IACA;IACA;IACAgF;MACA;MACA1B;MACA;IACA;IACA;IACA2B;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3qBA;AAAA;AAAA;AAAA;AAAk2C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACAt3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesGoEasy/chat_page/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesGoEasy/chat_page/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=23291a5a&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=23291a5a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"23291a5a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesGoEasy/chat_page/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=23291a5a&scoped=true&\"", "var components\ntry {\n  components = {\n    mScreenAnimationLihua: function () {\n      return import(\n        /* webpackChunkName: \"components/m-screen-animation-lihua/m-screen-animation-lihua\" */ \"@/components/m-screen-animation-lihua/m-screen-animation-lihua.vue\"\n      )\n    },\n    mScreenAnimationHongbao: function () {\n      return import(\n        /* webpackChunkName: \"components/m-screen-animation-hongbao/m-screen-animation-hongbao\" */ \"@/components/m-screen-animation-hongbao/m-screen-animation-hongbao.vue\"\n      )\n    },\n    mGroupSelection: function () {\n      return import(\n        /* webpackChunkName: \"components/m-group-selection/m-group-selection\" */ \"@/components/m-group-selection/m-group-selection.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.history.messages, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = !item.isHide ? _vm.renderMessageDate(item, index) : null\n    var m1 = !item.isHide && !item.recalled ? _vm.isSelf(item.senderId) : null\n    var m2 = !item.isHide && !!item.recalled ? _vm.isSelf(item.senderId) : null\n    var m3 =\n      !item.isHide && !!item.recalled\n        ? item.type === \"text\" && _vm.isSelf(item.senderId)\n        : null\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n      m2: m2,\n      m3: m3,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :id=\"page_font_size\">\r\n\t\t<view class=\"flex_c page\" @touchmove=\"touchmove\">\r\n\t\t\t<view class=\"navigationRef\">\r\n\t\t\t\t<navigation\r\n\t\t\t\t\tref=\"navigationRef\"\r\n\t\t\t\t\t:groupCount=\"groupCount\"\r\n\t\t\t\t\t:title=\"pagueObj.name\"\r\n\t\t\t\t\t:group_id=\"pagueObj.id\"\r\n\t\t\t\t></navigation>\r\n\t\t\t</view>\r\n\t\t\t<scroll-view\r\n\t\t\t\tclass=\"flex1 scroll-Y\"\r\n\t\t\t\**********=\"onPage\"\r\n\t\t\t\tid=\"scroll-view\"\r\n\t\t\t\tlower-threshold=\"100\"\r\n\t\t\t\tscroll-y\r\n\t\t\t\tscroll-with-animation\r\n\t\t\t\t:scroll-top=\"scroll_top\"\r\n\t\t\t\t@scroll=\"scroll\"\r\n\t\t\t\t@scrolltoupper=\"scrolltoupper\"\r\n\t\t\t\t@scrolltolower=\"scrolltolower\"\r\n\t\t\t>\r\n\t\t\t\t<view\r\n\t\t\t\t\tclass=\"scroll-view-str\"\r\n\t\t\t\t\t:style=\"{ height: `${reserveHeight}px` }\"\r\n\t\t\t\t\tv-if=\"reserveHeight > 0\"\r\n\t\t\t\t></view>\r\n\r\n\t\t\t\t<view class=\"messageList_\">\r\n\t\t\t\t\t<template v-for=\"(item, index) in history.messages\">\r\n\t\t\t\t\t\t<!-- #ifdef APP || H5 -->\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tclass=\"z_index2\"\r\n\t\t\t\t\t\t\t:class=\"`oneheight_${index}`\"\r\n\t\t\t\t\t\t\tstyle=\"transform: rotate(-180deg)\"\r\n\t\t\t\t\t\t\t:key=\"item.messageId + index\"\r\n\t\t\t\t\t\t\tv-if=\"!item.isHide\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<view class=\"icon_ text_26 color__ time\">\r\n\t\t\t\t\t\t\t\t{{ renderMessageDate(item, index) }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view :key=\"item.messageId + index\" v-if=\"!item.recalled\">\r\n\t\t\t\t\t\t\t\t<item\r\n\t\t\t\t\t\t\t\t\t:isMy=\"isSelf(item.senderId)\"\r\n\t\t\t\t\t\t\t\t\t:myid=\"myid\"\r\n\t\t\t\t\t\t\t\t\t:item=\"item\"\r\n\t\t\t\t\t\t\t\t\t@onClick=\"onItem\"\r\n\t\t\t\t\t\t\t\t\t@onLongpress=\"onLongpress\"\r\n\t\t\t\t\t\t\t\t\t@mention=\"mention\"\r\n\t\t\t\t\t\t\t\t\t@imgLoad=\"imgLoad\"\r\n\t\t\t\t\t\t\t\t></item>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"icon_ text_26 recalled\" v-else>\r\n\t\t\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t\t\t<text v-if=\"isSelf(item.senderId)\">你</text>\r\n\t\t\t\t\t\t\t\t\t<text v-else>{{ item.senderData.name }}</text>\r\n\t\t\t\t\t\t\t\t\t撤回了一条消息\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\t\t\tclass=\"recalled-edit\"\r\n\t\t\t\t\t\t\t\t\tv-if=\"item.type === 'text' && isSelf(item.senderId)\"\r\n\t\t\t\t\t\t\t\t\t@click=\"recalledEdit(item)\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t重新编辑\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t<!-- #ifdef MP -->\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tclass=\"z_index2\"\r\n\t\t\t\t\t\t\tstyle=\"transform: rotate(-180deg)\"\r\n\t\t\t\t\t\t\t:key=\"item.messageId\"\r\n\t\t\t\t\t\t\tv-if=\"!item.isHide\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<view class=\"icon_ text_26 color__ time\">\r\n\t\t\t\t\t\t\t\t{{ renderMessageDate(item, index) }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view :key=\"item.messageIds\" v-if=\"!item.recalled\">\r\n\t\t\t\t\t\t\t\t<item\r\n\t\t\t\t\t\t\t\t\t:isMy=\"isSelf(item.senderId)\"\r\n\t\t\t\t\t\t\t\t\t:myid=\"myid\"\r\n\t\t\t\t\t\t\t\t\t:item=\"item\"\r\n\t\t\t\t\t\t\t\t\t@onClick=\"onItem\"\r\n\t\t\t\t\t\t\t\t\t@onLongpress=\"onLongpress\"\r\n\t\t\t\t\t\t\t\t\t@mention=\"mention\"\r\n\t\t\t\t\t\t\t\t></item>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"icon_ text_26 recalled\" v-else>\r\n\t\t\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t\t\t<text v-if=\"isSelf(item.senderId)\">你</text>\r\n\t\t\t\t\t\t\t\t\t<text v-else>{{ item.senderData.name }}</text>\r\n\t\t\t\t\t\t\t\t\t撤回了一条消息\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\t\t\tclass=\"recalled-edit\"\r\n\t\t\t\t\t\t\t\t\tv-if=\"item.type === 'text' && isSelf(item.senderId)\"\r\n\t\t\t\t\t\t\t\t\t@click=\"recalledEdit(item)\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t重新编辑\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view :style=\"{ height: $store.state.StatusBar.customBar - 8 + 4 + 'px' }\"></view>\r\n\t\t\t</scroll-view>\r\n\t\t\t<view class=\"bottomOperationRef\">\r\n\t\t\t\t<bottom-operation\r\n\t\t\t\t\tref=\"bottomOperationRef\"\r\n\t\t\t\t\t:to=\"to\"\r\n\t\t\t\t\t:userList=\"userList\"\r\n\t\t\t\t\t@pushList=\"pushList\"\r\n\t\t\t\t\t@onBottom=\"onBottom\"\r\n\t\t\t\t\t@backToBottom=\"bottomOperationScrollToBottom\"\r\n\t\t\t\t\t@focus=\"focus\"\r\n\t\t\t\t\t@keyboardheightchange=\"keyboardheightchange\"\r\n\t\t\t\t></bottom-operation>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 礼花 -->\r\n\t\t<m-screen-animation-lihua\r\n\t\t\tref=\"mScreenAnimationLihua\"\r\n\t\t\tzIndex=\"9999\"\r\n\t\t></m-screen-animation-lihua>\r\n\t\t<!-- 红包雨 -->\r\n\t\t<m-screen-animation-hongbao ref=\"mScreenAnimationHongbao\"></m-screen-animation-hongbao>\r\n\t\t<!-- 视频播放器 -->\r\n\t\t<video-player-ref\r\n\t\t\tv-model=\"videoPlayer.show\"\r\n\t\t\t:url=\"videoPlayer.url\"\r\n\t\t\t@onVideoFullScreenChange=\"onVideoFullScreenChange\"\r\n\t\t></video-player-ref>\r\n\t\t<!-- 红包封面 -->\r\n\t\t<open-red-packet ref=\"openRedPacketRef\"></open-red-packet>\r\n\t\t<!-- 复制；撤回等操作 -->\r\n\t\t<operate ref=\"operateRef\" @quote=\"quote\" @thank=\"thank\" @transmit=\"transmit\"></operate>\r\n\t\t<!-- 转发选择聊天 -->\r\n\t\t<m-group-selection ref=\"groupSelectionRef\" @sendMessage=\"sendMessage\"></m-group-selection>\r\n\t</view>\r\n</template>\r\n<script>\r\nimport { 自己的信息, 对话数据 } from '@/TEST/index.js'\r\nimport navigation from './components/navigation/index.vue'\r\nimport bottomOperation from './components/bottom-operation/index.vue'\r\nimport item from './components/item/index'\r\nimport videoPlayerRef from './components/video-player/index'\r\nimport openRedPacket from './components/open-red-packet/index'\r\nimport operate from './components/operate/index'\r\nimport { mapState } from 'vuex'\r\n\r\nlet lastMessageTimeStamp = null\r\n\r\nlet userInforMap = {}\r\n\r\nlet envelopeClickList = []\r\nlet innerAudioContext = uni.createInnerAudioContext()\r\nlet audioItem = {}\r\nlet group = {}\r\n\r\nlet groupId = null\r\n\r\n// 浏览照片数组\r\nlet imageList = []\r\n\r\n// 是否是手动触发的列表滑动\r\nlet isBottomOperationScrollToBottom = false\r\n\r\nimport { show, formatDate, throttle, openimg, getLocation, to as tofn } from '@/utils/index.js'\r\n\r\nconst IMAGE_MAX_WIDTH = 200\r\nconst IMAGE_MAX_HEIGHT = 150\r\nlet scroll_top = 0\r\nlet reserveHeightRef = 0\r\nlet bottomOperationRefHeight = 0\r\nexport default {\r\n\tcomponents: {\r\n\t\t// groupSelection,\r\n\t\tnavigation,\r\n\t\tbottomOperation,\r\n\t\titem,\r\n\t\tvideoPlayerRef,\r\n\t\topenRedPacket,\r\n\t\toperate,\r\n\t},\r\n\tname: 'groupChat',\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisHistoryGet: false,\r\n\t\t\treserveHeight: 0,\r\n\t\t\tkeyboardheightchangeValue: 0,\r\n\t\t\tmyid: null,\r\n\t\t\tscroll_top,\r\n\t\t\tuserList: [], //群成员列表\r\n\t\t\tgroupCount: '',\r\n\t\t\tpagueObj: {\r\n\t\t\t\tname: '饭搭子5人组',\r\n\t\t\t},\r\n\t\t\tto: {},\r\n\t\t\t// 历史数据\r\n\t\t\thistory: {\r\n\t\t\t\tmessages: [],\r\n\t\t\t\tallLoaded: false,\r\n\t\t\t},\r\n\t\t\tvideoPlayer: {\r\n\t\t\t\tshow: false,\r\n\t\t\t\turl: '',\r\n\t\t\t\tcontext: null,\r\n\t\t\t},\r\n\t\t}\r\n\t},\r\n\tasync onLoad(e) {\r\n\t\tscroll_top = 0\r\n\t\tthis.scroll_top = scroll_top\r\n\t\timageList = []\r\n\t\tenvelopeClickList = uni.getStorageSync('envelopeClickList') || []\r\n\t\tlastMessageTimeStamp = e.lastMessageTimeStamp || null\r\n\t\tthis.isHistoryGet = e.lastMessageTimeStamp\r\n\t\tgroupId = e.groupId\r\n\t\t// 两次进入同一个群就读取第一次进入缓存的数据/否者清空缓存\r\n\t\tif (userInforMap[groupId] !== groupId) {\r\n\t\t\tuserInforMap = {}\r\n\t\t\tuserInforMap[groupId] = groupId\r\n\t\t}\r\n\t\tconst { member_id = '' } = 自己的信息\r\n\t\tthis.myid = member_id\r\n\t\tthis.loadHistoryMessage()\r\n\t\tthis.$nextTick(() => {\r\n\t\t\tlet view = uni.createSelectorQuery().select('.bottomOperationRef')\r\n\t\t\tview.boundingClientRect((ref) => {\r\n\t\t\t\tbottomOperationRefHeight = ref.height\r\n\t\t\t}).exec()\r\n\t\t})\r\n\t},\r\n\tonPageScroll() {\r\n\t\tthis.$refs.bottomOperationRef.closeAll()\r\n\t},\r\n\tonReady() {\r\n\t\tthis.videoPlayer.context = uni.createVideoContext('videoPlayer', this)\r\n\t},\r\n\tcomputed: mapState({\r\n\t\tpage_font_size: (state) => state.page_font_size,\r\n\t\t//显示时间\r\n\t\trenderMessageDate() {\r\n\t\t\treturn (message, index) => {\r\n\t\t\t\tif (\r\n\t\t\t\t\tmessage.timestamp - this.history.messages[index + 1]?.timestamp >\r\n\t\t\t\t\t3 * 60 * 1000\r\n\t\t\t\t) {\r\n\t\t\t\t\treturn formatDate(message.timestamp, 'timestamp')\r\n\t\t\t\t}\r\n\t\t\t\treturn ''\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 是否本人isMy\r\n\t\tisSelf() {\r\n\t\t\treturn (senderId) => {\r\n\t\t\t\tconst { member_id = '' } = 自己的信息\r\n\t\t\t\treturn senderId === `${member_id}`\r\n\t\t\t}\r\n\t\t},\r\n\t\tenvelope_top_opened() {\r\n\t\t\treturn (id) => {\r\n\t\t\t\treturn this.envelopeXollectionList.includes(id)\r\n\t\t\t}\r\n\t\t},\r\n\t}),\r\n\r\n\tmethods: {\r\n\t\tsetHeight(e) {\r\n\t\t\tconst res = uni.getSystemInfoSync()\r\n\t\t\tconst windowHeight = res.windowHeight\r\n\t\t\t// 20 名字向上偏移\r\n\t\t\t// 8 内边距补偿\r\n\t\t\t// 4 名字偏移补偿\r\n\t\t\tconst customBar = this.$store.state.StatusBar.customBar - 8 + 4\r\n\t\t\tconst reserveHeight = windowHeight - e - customBar - bottomOperationRefHeight\r\n\t\t\tthis.reserveHeight = reserveHeight\r\n\t\t\treserveHeightRef = reserveHeight\r\n\t\t},\r\n\r\n\t\tgetHeight(e) {\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tlet view = uni.createSelectorQuery().select('.messageList_')\r\n\t\t\t\tview.boundingClientRect((select) => {\r\n\t\t\t\t\tif (!select) return\r\n\t\t\t\t\tif (!select?.height) {\r\n\t\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\t\tlet view2 = uni.createSelectorQuery().select('.messageList_')\r\n\t\t\t\t\t\t\tview2\r\n\t\t\t\t\t\t\t\t.boundingClientRect((select) => {\r\n\t\t\t\t\t\t\t\t\tthis.setHeight(select.height)\r\n\t\t\t\t\t\t\t\t\tif (e) {\r\n\t\t\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\t\t\tthis.reserveHeight =\r\n\t\t\t\t\t\t\t\t\t\t\t\tthis.reserveHeight - this.keyboardheightchangeValue\r\n\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t.exec()\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.setHeight(select.height)\r\n\t\t\t\t\t\tif (e) {\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\tthis.reserveHeight =\r\n\t\t\t\t\t\t\t\t\tthis.reserveHeight - this.keyboardheightchangeValue\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}).exec()\r\n\t\t\t})\r\n\t\t},\r\n\r\n\t\t//图片加载完成\r\n\t\timgLoad(e) {\r\n\t\t\tif (this.history.messages.length > 20) return\r\n\t\t\tthis.getHeight(true)\r\n\t\t},\r\n\t\tkeyboardheightchange(e, e2 = false) {\r\n\t\t\tthis.keyboardheightchangeValue = e\r\n\t\t\tif (reserveHeightRef) {\r\n\t\t\t\tthis.reserveHeight = reserveHeightRef - e\r\n\t\t\t}\r\n\t\t\tif (e === 0) {\r\n\t\t\t\tif (e2) return\r\n\t\t\t\tthis.getHeight()\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 点击整个页面\r\n\t\tonPage() {\r\n\t\t\tthis.$refs.bottomOperationRef.close()\r\n\t\t\tthis.$refs.operateRef.close()\r\n\t\t},\r\n\t\ttouchmove() {\r\n\t\t\t// this.$refs.bottomOperationRef.closeAll();\r\n\t\t},\r\n\t\tonBottom() {\r\n\t\t\tthis.$refs.operateRef.close()\r\n\t\t},\r\n\t\t// 输入框获取焦点\r\n\t\tfocus() {\r\n\t\t\tif (this.isHistoryGet) {\r\n\t\t\t\tthis.isHistoryGet = false\r\n\t\t\t\tlastMessageTimeStamp = null\r\n\t\t\t\tthis.history.messages = []\r\n\t\t\t\tthis.loadHistoryMessage()\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 获取聊天记录\r\n\t\tloadHistoryMessage() {\r\n\t\t\tuni.hideLoading()\r\n\t\t\tlet list = JSON.parse(JSON.stringify(对话数据))\r\n\t\t\tlist = list.reverse()\r\n\t\t\t// 同步混入数据\r\n\t\t\tlist.forEach((im, ix) => {\r\n\t\t\t\t// 缓存照片地址，\r\n\t\t\t\tif (im.type === 'image' || im.type === 'image_transmit') {\r\n\t\t\t\t\timageList.unshift(im.payload.url)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t// 模拟只有少量数据\r\n\t\t\t// this.history.messages = [list[0],list[1],list[2]];\r\n\t\t\tthis.history.messages = [...this.history.messages, ...list]\r\n\t\t\tif (this.history.messages.length > 20) return\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis.getHeight()\r\n\t\t\t})\r\n\t\t},\r\n\t\tonMessageReceived(message) {\r\n\t\t\tif (message.groupId === group.id) {\r\n\t\t\t\t// push进列表\r\n\t\t\t\tthis.pushList(message)\r\n\t\t\t\t//聊天时，收到消息标记为已读\r\n\t\t\t\tthis.markGroupMessageAsRead()\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 转发成功后\r\n\t\tsendMessage(message) {\r\n\t\t\t// push进列表\r\n\t\t\tif (message.groupId === groupId) {\r\n\t\t\t\tthis.pushList(message)\r\n\t\t\t\t// 同步消息到首页\r\n\t\t\t\tuni.$emit('onMessageReceived', message)\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 将信息设置为已读\r\n\t\tmarkGroupMessageAsRead() {\r\n\t\t\t//\r\n\t\t},\r\n\t\t// 组装item\r\n\t\tinitMessageItem(message, index) {\r\n\t\t\tmessage['isHide'] = 0\r\n\t\t\t// 初始化语音\r\n\t\t\tif (message.type === 'audio') {\r\n\t\t\t\tmessage['pause'] = 4\r\n\t\t\t}\r\n\t\t\t// 初始化红包\r\n\t\t\tif (message.type === 'red_envelope') {\r\n\t\t\t\tmessage['had_draw'] = 0\r\n\t\t\t\tmessage['isClick'] = 0\r\n\t\t\t\tthis.setEnvelopeClickList(message, index)\r\n\t\t\t}\r\n\t\t\tif (index === 0 && (message.type === 'text' || message.type === 'text_quote')) {\r\n\t\t\t\tthis.onSetText(message.payload.text)\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 处理红包是否被点击\r\n\t\tsetEnvelopeClickList(im, index) {\r\n\t\t\tif (envelopeClickList.includes(im.messageId)) {\r\n\t\t\t\tim['isClick'] = 1\r\n\t\t\t} else {\r\n\t\t\t\tim['isClick'] = 0\r\n\t\t\t\tif (index === 0) {\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tthis.$refs.mScreenAnimationHongbao.show()\r\n\t\t\t\t\t\tuni.vibrateLong()\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 发送信息后，将信息push到列表\r\n\t\tasync pushList(message) {\r\n\t\t\tthis.initMessageItem(message)\r\n\t\t\t// 监听到公告\r\n\t\t\tif (message.type === 'group_notice') {\r\n\t\t\t\tconsole.log('监听到公告')\r\n\t\t\t\tthis.$refs.navigationRef.getData()\r\n\t\t\t}\r\n\t\t\t// 监听到修改群名\r\n\t\t\tif (message.type === 'update_group_name') {\r\n\t\t\t\tconsole.log('监听到修改群名')\r\n\t\t\t\tthis.pagueObj.name = message.payload.name\r\n\t\t\t}\r\n\t\t\tthis.history.messages.unshift(message)\r\n\t\t\tthis.scrollToBottom()\r\n\r\n\t\t\tif (this.history.messages.length < 20) {\r\n\t\t\t\tthis.getHeight(true)\r\n\t\t\t}\r\n\r\n\t\t\t// 是否触发文字动效果\r\n\t\t\tif (message.type === 'text' || message.type === 'text_quote') {\r\n\t\t\t\tthis.onSetText(message.payload.text)\r\n\t\t\t}\r\n\t\t\t// 是否触发红包雨\r\n\t\t\tif (message.type === 'red_envelope') {\r\n\t\t\t\tthis.onSetRedEnvelope()\r\n\t\t\t}\r\n\r\n\t\t\t// 缓存照片地址，\r\n\t\t\tif (message.type === 'image' || message.type === 'image_transmit') {\r\n\t\t\t\timageList.push(message.payload.url)\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 文本触发效果相关========\r\n\t\tonSetText(text) {\r\n\t\t\t// 触发礼花\r\n\t\t\tthrottle(() => {\r\n\t\t\t\tif (text.includes('[彩带]')) {\r\n\t\t\t\t\tthis.$refs.mScreenAnimationLihua.show()\r\n\t\t\t\t\tuni.vibrateLong()\r\n\t\t\t\t}\r\n\t\t\t}, 4000)\r\n\t\t},\r\n\t\t// 触发红包雨\r\n\t\tonSetRedEnvelope() {\r\n\t\t\tthrottle(() => {\r\n\t\t\t\tthis.$refs.mScreenAnimationHongbao.show()\r\n\t\t\t\tuni.vibrateLong()\r\n\t\t\t}, 4000)\r\n\t\t},\r\n\t\tbottomOperationScrollToBottom() {\r\n\t\t\tisBottomOperationScrollToBottom = true\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tisBottomOperationScrollToBottom = false\r\n\t\t\t}, 800)\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis.scrollToBottom()\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 页面滚动到底部\r\n\t\tscrollToBottom(e) {\r\n\t\t\tthis.scroll_top = scroll_top\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis.scroll_top = 0\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 点击某条信息\r\n\t\tonItem(item, index) {\r\n\t\t\tconsole.log(item)\r\n\t\t\tswitch (item.type) {\r\n\t\t\t\tcase 'video':\r\n\t\t\t\t\tthis.playVideo(item)\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 'audio':\r\n\t\t\t\t\tthis.playAudio(item)\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 'audio_quote':\r\n\t\t\t\t\tthis.playAudio(item)\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 'image':\r\n\t\t\t\tcase 'image_transmit':\r\n\t\t\t\t\tconst index = imageList.indexOf(item.payload.url)\r\n\t\t\t\t\tif (index === -1) return openimg(imageList.length - 1, imageList)\r\n\t\t\t\t\topenimg(index, imageList)\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 'red_envelope':\r\n\t\t\t\t\t// 点击红包\r\n\t\t\t\t\tconst fun = (code) => {\r\n\t\t\t\t\t\tthis.renewItem(code, item)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tuni.$off('open_red_packet')\r\n\t\t\t\t\tuni.$on('open_red_packet', fun)\r\n\t\t\t\t\titem['id'] = group.id\r\n\t\t\t\t\tthis.$refs.openRedPacketRef.open(item)\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 'map':\r\n\t\t\t\t\tgetLocation({\r\n\t\t\t\t\t\tname: item.payload.title,\r\n\t\t\t\t\t\taddress: item.payload.address,\r\n\t\t\t\t\t\tlatitude: item.payload.latitude,\r\n\t\t\t\t\t\tlongitude: item.payload.longitude,\r\n\t\t\t\t\t})\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 'article':\r\n\t\t\t\t\ttofn(`/pagesOne/HTML/index?id=${item.payload.id}`)\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 'share_SBCF':\r\n\t\t\t\t\ttofn('/pagesSBCF/commodity_list/index', {\r\n\t\t\t\t\t\tid: item.payload.seller_id,\r\n\t\t\t\t\t})\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 'share_mall':\r\n\t\t\t\t\ttofn(`/pagesShopping/details/index`, {\r\n\t\t\t\t\t\tgoods_id: item.payload.goods_id,\r\n\t\t\t\t\t})\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 'functional_module':\r\n\t\t\t\t\ttofn(item.payload.url)\r\n\t\t\t\t\tbreak\r\n\t\t\t\tdefault:\r\n\t\t\t\t\tbreak\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 点击红包后更新那一条\r\n\t\trenewItem(code, item) {\r\n\t\t\tif (code === '0') {\r\n\t\t\t\t// 领取\r\n\t\t\t\titem.had_draw = 1\r\n\t\t\t} else {\r\n\t\t\t\titem.isClick = 1\r\n\t\t\t}\r\n\t\t\t// 不这样写某些情况下更新不了视图，\r\n\t\t\tfor (let i = 0; i < this.history.messages.length; i++) {\r\n\t\t\t\tif (this.history.messages[i].messageId == item.messageId) {\r\n\t\t\t\t\tthis.$set(this.history.messages, i, {\r\n\t\t\t\t\t\t...item,\r\n\t\t\t\t\t})\r\n\t\t\t\t\tbreak\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 长按相关=======================\r\n\t\t// 长按某一条\r\n\t\tonLongpress(item, e) {\r\n\t\t\tthis.$refs.operateRef.open(item, e)\r\n\t\t},\r\n\t\t// 引用\r\n\t\tquote(item) {\r\n\t\t\tthis.$refs.bottomOperationRef.quote(item)\r\n\t\t},\r\n\t\t// 谢谢红包\r\n\t\tthank(item) {\r\n\t\t\tthis.$refs.bottomOperationRef.thank(item)\r\n\t\t},\r\n\t\t// 转发\r\n\t\ttransmit(item) {\r\n\t\t\tthis.$refs.groupSelectionRef.open(item)\r\n\t\t},\r\n\t\t// 重新编辑\r\n\t\trecalledEdit(item) {\r\n\t\t\tthis.$refs.bottomOperationRef.recalledEdit(item)\r\n\t\t},\r\n\t\t// @某人\r\n\t\tmention(item) {\r\n\t\t\tthis.$refs.bottomOperationRef.mention(item)\r\n\t\t},\r\n\t\t// 视频相关========================\r\n\t\t// 点击了视频并播放\r\n\t\tplayVideo(item) {\r\n\t\t\tthis.videoPlayer.url = item.payload.video.url\r\n\t\t\tthis.videoPlayer.show = true\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis.videoPlayer.context.requestFullScreen({\r\n\t\t\t\t\tdirection: 0,\r\n\t\t\t\t})\r\n\t\t\t\tthis.videoPlayer.context.play()\r\n\t\t\t\tthis.videoPlayer.context.showStatusBar()\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 退出全屏\r\n\t\tonVideoFullScreenChange(e) {\r\n\t\t\t//当退出全屏播放时，隐藏播放器\r\n\t\t\tif (this.videoPlayer.show && !e.detail.fullScreen) {\r\n\t\t\t\tthis.videoPlayer.show = false\r\n\t\t\t\tthis.videoPlayer.context.stop()\r\n\t\t\t}\r\n\t\t},\r\n\t\t// =============================================\r\n\t\t// 播放语音相关===========\r\n\t\tplayAudio(item) {\r\n\t\t\tthrottle(() => {\r\n\t\t\t\t// pause:1暂停;2播放完,3播放中,4初始状态\r\n\t\t\t\tif (item.messageId === audioItem?.messageId) {\r\n\t\t\t\t\tif (audioItem['pause'] == 3) {\r\n\t\t\t\t\t\t//正在播放\r\n\t\t\t\t\t\t// 暂停\r\n\t\t\t\t\t\tinnerAudioContext.pause()\r\n\t\t\t\t\t\tinnerAudioContext.offEnded()\r\n\t\t\t\t\t\titem['pause'] = 1\r\n\t\t\t\t\t\taudioItem['pause'] = 1\r\n\t\t\t\t\t} else if (audioItem['pause'] == 1 || audioItem['pause'] == 2) {\r\n\t\t\t\t\t\t//暂停或者播放中\r\n\t\t\t\t\t\t// 播放\r\n\t\t\t\t\t\tinnerAudioContext.play()\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\taudioItem['pause'] = '4'\r\n\t\t\t\taudioItem = item\r\n\t\t\t\tif (innerAudioContext) {\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tinnerAudioContext.pause()\r\n\t\t\t\t\t\tinnerAudioContext.destroy()\r\n\t\t\t\t\t\tinnerAudioContext = null\r\n\t\t\t\t\t} catch (e) {}\r\n\t\t\t\t}\r\n\t\t\t\tinnerAudioContext = uni.createInnerAudioContext()\r\n\t\t\t\tinnerAudioContext.src = item.payload.url\r\n\t\t\t\tinnerAudioContext.play()\r\n\t\t\t\tinnerAudioContext.offEnded()\r\n\t\t\t\tinnerAudioContext.offPlay()\r\n\t\t\t\tinnerAudioContext.onPlay(() => {\r\n\t\t\t\t\t// console.log('开始播放');\r\n\t\t\t\t\titem['pause'] = 3\r\n\t\t\t\t\taudioItem['pause'] = 3\r\n\t\t\t\t})\r\n\t\t\t\tinnerAudioContext.onEnded(() => {\r\n\t\t\t\t\t// console.log('播放结束');\r\n\t\t\t\t\titem['pause'] = 2\r\n\t\t\t\t\taudioItem['pause'] = 2\r\n\t\t\t\t})\r\n\t\t\t\tinnerAudioContext.onError((res) => {\r\n\t\t\t\t\tconsole.log('播放异常')\r\n\t\t\t\t})\r\n\t\t\t}, 500)\r\n\t\t},\r\n\t\t// ====================\r\n\r\n\t\t// 滚动中\r\n\t\tscroll(e) {\r\n\t\t\tscroll_top = e.detail.scrollTop\r\n\t\t\tthis.$refs.operateRef.close()\r\n\t\t\tif (isBottomOperationScrollToBottom) return\r\n\t\t\tthis.$refs.bottomOperationRef.closeAll()\r\n\t\t},\r\n\t\t// 滚动到底部\r\n\t\tscrolltolower() {\r\n\t\t\tif (this.history.allLoaded) return\r\n\t\t\tconsole.log('触底')\r\n\t\t\tthis.loadHistoryMessage()\r\n\t\t},\r\n\t\t// 滚动到顶部\r\n\t\tscrolltoupper() {},\r\n\t},\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.page {\r\n\tposition: fixed;\r\n\tz-index: 1;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tbottom: 0;\r\n\tright: 0;\r\n\tbackground-color: #ededed;\r\n}\r\n\r\n.scroll-Y {\r\n\twidth: 100%;\r\n\theight: 0;\r\n\ttransition: all 0.2s;\r\n\ttransform: rotate(180deg);\r\n\tbackground-color: #ededed;\r\n\r\n\t::-webkit-scrollbar {\r\n\t\tdisplay: none;\r\n\t}\r\n}\r\n\r\n.scroll-view-str {\r\n\twidth: 100%;\r\n}\r\n\r\n.time {\r\n\twidth: 100%;\r\n\tcolor: #a3a3a3;\r\n\tline-height: 100rpx;\r\n}\r\n\r\n.recalled {\r\n\twidth: 100%;\r\n\theight: 50rpx;\r\n\tmargin: 20rpx 0;\r\n\tcolor: #a3a3a3;\r\n\r\n\t.recalled-edit {\r\n\t\tcolor: #5a6693;\r\n\t\tmargin-left: 14rpx;\r\n\t}\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=23291a5a&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=23291a5a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754983153407\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}