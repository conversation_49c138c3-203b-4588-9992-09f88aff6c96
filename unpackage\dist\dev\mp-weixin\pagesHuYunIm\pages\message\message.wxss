@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* ==================== 主容器样式 ==================== */
.chat-container.data-v-0b926c58 {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}
/* ==================== 顶部导航栏 ==================== */
.chat-header.data-v-0b926c58 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #e5e5e5;
}
.chat-header .header-left.data-v-0b926c58 {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.chat-header .header-left .back-icon.data-v-0b926c58 {
  font-size: 40rpx;
  color: #007aff;
  font-weight: bold;
}
.chat-header .header-center.data-v-0b926c58 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.chat-header .header-center .chat-title.data-v-0b926c58 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.chat-header .header-center .online-status.data-v-0b926c58 {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
}
.chat-header .header-right.data-v-0b926c58 {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.chat-header .header-right .more-icon.data-v-0b926c58 {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}
/* ==================== 消息列表 ==================== */
.message-list.data-v-0b926c58 {
  flex: 1;
  background-color: #f5f5f5;
}
.message-list .load-more.data-v-0b926c58 {
  display: flex;
  justify-content: center;
  padding: 20rpx;
}
.message-list .load-more .loading-text.data-v-0b926c58 {
  font-size: 28rpx;
  color: #999;
}
.message-list .message-content.data-v-0b926c58 {
  padding: 20rpx 30rpx 120rpx;
}
.message-list .bottom-placeholder.data-v-0b926c58 {
  height: 20rpx;
}
/* ==================== 时间分隔线 ==================== */
.time-divider.data-v-0b926c58 {
  display: flex;
  justify-content: center;
  margin: 30rpx 0;
}
.time-divider .time-text.data-v-0b926c58 {
  padding: 8rpx 20rpx;
  font-size: 24rpx;
  color: #999;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 20rpx;
}
/* ==================== 消息项 ==================== */
.message-item.data-v-0b926c58 {
  display: flex;
  margin-bottom: 30rpx;
}
.message-item.self-message.data-v-0b926c58 {
  flex-direction: row-reverse;
}
.message-item.self-message .message-body.data-v-0b926c58 {
  align-items: flex-end;
}
.message-item.self-message .message-bubble.data-v-0b926c58 {
  background-color: #95ec69;
}
.message-item.self-message .message-bubble.data-v-0b926c58::after {
  right: -12rpx;
  border-left-color: #95ec69;
  border-right: none;
}
.message-item.friend-message .message-bubble.data-v-0b926c58 {
  background-color: #fff;
}
.message-item.friend-message .message-bubble.data-v-0b926c58::after {
  left: -12rpx;
  border-right-color: #fff;
  border-left: none;
}
/* ==================== 头像容器 ==================== */
.avatar-container.data-v-0b926c58 {
  margin: 0 20rpx;
}
.avatar-container .avatar.data-v-0b926c58 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
}
/* ==================== 消息主体 ==================== */
.message-body.data-v-0b926c58 {
  display: flex;
  flex-direction: column;
  max-width: 60%;
}
.message-body .nickname.data-v-0b926c58 {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
  padding: 0 20rpx;
}
/* ==================== 消息气泡 ==================== */
.message-bubble.data-v-0b926c58 {
  position: relative;
  padding: 20rpx;
  border-radius: 12rpx;
  word-wrap: break-word;
  word-break: break-all;
}
.message-bubble.data-v-0b926c58::after {
  content: "";
  position: absolute;
  top: 20rpx;
  width: 0;
  height: 0;
  border: 12rpx solid transparent;
}
.message-bubble .text-content.data-v-0b926c58 {
  font-size: 32rpx;
  line-height: 1.4;
  color: #333;
}
.message-bubble .image-content .message-image.data-v-0b926c58 {
  max-width: 400rpx;
  max-height: 400rpx;
  border-radius: 8rpx;
}
.message-bubble .voice-content.data-v-0b926c58 {
  display: flex;
  align-items: center;
  min-width: 120rpx;
}
.message-bubble .voice-content.playing .voice-icon.data-v-0b926c58 {
  -webkit-animation: voice-wave-data-v-0b926c58 1s infinite;
          animation: voice-wave-data-v-0b926c58 1s infinite;
}
.message-bubble .voice-content .voice-icon.data-v-0b926c58 {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
}
.message-bubble .voice-content .voice-icon.voice-animation.data-v-0b926c58 {
  -webkit-animation: voice-wave-data-v-0b926c58 1s infinite;
          animation: voice-wave-data-v-0b926c58 1s infinite;
}
.message-bubble .voice-content .voice-duration.data-v-0b926c58 {
  font-size: 28rpx;
  color: #333;
}
.message-bubble .message-status.data-v-0b926c58 {
  position: absolute;
  right: -40rpx;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
.message-bubble .message-status .status-icon.data-v-0b926c58 {
  font-size: 24rpx;
  color: #999;
}
.message-bubble .message-status .status-icon.read.data-v-0b926c58 {
  color: #07c160;
}
/* ==================== 输入工具栏 ==================== */
.input-toolbar.data-v-0b926c58 {
  position: fixed;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top: 1rpx solid #e5e5e5;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  z-index: 1000;
}
/* ==================== 语音模式 ==================== */
.voice-mode .voice-controls.data-v-0b926c58 {
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.voice-mode .voice-controls .mode-switch.data-v-0b926c58 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
}
.voice-mode .voice-controls .voice-button.data-v-0b926c58 {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 8rpx;
}
.voice-mode .voice-controls .voice-button.recording.data-v-0b926c58 {
  background-color: #ff4757;
}
.voice-mode .voice-controls .voice-button .voice-text.data-v-0b926c58 {
  font-size: 32rpx;
  color: #333;
}
.recording .voice-mode .voice-controls .voice-button .voice-text.data-v-0b926c58 {
  color: #fff;
}
/* ==================== 文本模式 ==================== */
.text-mode .input-controls.data-v-0b926c58 {
  display: flex;
  align-items: flex-end;
  gap: 20rpx;
}
.text-mode .input-controls .voice-btn.data-v-0b926c58,
.text-mode .input-controls .emoji-btn.data-v-0b926c58,
.text-mode .input-controls .more-btn.data-v-0b926c58 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #666;
  background-color: #f5f5f5;
  border-radius: 8rpx;
}
.text-mode .input-controls .input-wrapper.data-v-0b926c58 {
  flex: 1;
}
.text-mode .input-controls .input-wrapper .text-input.data-v-0b926c58 {
  width: 100%;
  min-height: 60rpx;
  max-height: 120rpx;
  padding: 16rpx 20rpx;
  font-size: 32rpx;
  line-height: 1.4;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  border: none;
  outline: none;
  resize: none;
}
.text-mode .input-controls .send-btn.data-v-0b926c58 {
  padding: 16rpx 32rpx;
  background-color: #07c160;
  border-radius: 8rpx;
}
.text-mode .input-controls .send-btn .send-text.data-v-0b926c58 {
  font-size: 28rpx;
  color: #fff;
}
/* ==================== 表情面板 ==================== */
.emoji-panel.data-v-0b926c58 {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 500rpx;
  background-color: #fff;
  border-top: 1rpx solid #e5e5e5;
  z-index: 999;
}
/* ==================== 更多功能面板 ==================== */
.more-panel.data-v-0b926c58 {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-top: 1rpx solid #e5e5e5;
  padding: 40rpx 30rpx;
  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
  z-index: 999;
}
.more-panel .more-grid.data-v-0b926c58 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40rpx;
}
.more-panel .more-grid .more-item.data-v-0b926c58 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}
.more-panel .more-grid .more-item .more-icon.data-v-0b926c58 {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  background-color: #f5f5f5;
  border-radius: 20rpx;
}
.more-panel .more-grid .more-item .more-text.data-v-0b926c58 {
  font-size: 24rpx;
  color: #666;
}
/* ==================== 录音动画覆盖层 ==================== */
.recording-overlay.data-v-0b926c58 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}
.recording-overlay .recording-animation.data-v-0b926c58 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;
  padding: 60rpx;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 20rpx;
}
.recording-overlay .recording-animation .wave-container.data-v-0b926c58 {
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.recording-overlay .recording-animation .wave-container .wave.data-v-0b926c58 {
  width: 8rpx;
  height: 40rpx;
  background-color: #07c160;
  border-radius: 4rpx;
  -webkit-animation: wave-animation-data-v-0b926c58 1s ease-in-out infinite alternate;
          animation: wave-animation-data-v-0b926c58 1s ease-in-out infinite alternate;
}
.recording-overlay .recording-animation .recording-text.data-v-0b926c58 {
  font-size: 32rpx;
  color: #fff;
  font-weight: 600;
}
.recording-overlay .recording-animation .recording-tip.data-v-0b926c58 {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}
/* ==================== 动画定义 ==================== */
@-webkit-keyframes voice-wave-data-v-0b926c58 {
0%, 100% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
50% {
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
}
}
@keyframes voice-wave-data-v-0b926c58 {
0%, 100% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
50% {
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
}
}
@-webkit-keyframes wave-animation-data-v-0b926c58 {
0% {
    height: 20rpx;
}
100% {
    height: 80rpx;
}
}
@keyframes wave-animation-data-v-0b926c58 {
0% {
    height: 20rpx;
}
100% {
    height: 80rpx;
}
}
/* ==================== 兼容旧版本样式 ==================== */
.message.data-v-0b926c58 {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
}
.message .avatar.data-v-0b926c58 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 10rpx;
  margin-right: 30rpx;
  margin-top: 20rpx;
}
.message .content.data-v-0b926c58 {
  min-height: 80rpx;
  max-width: 60vw;
  box-sizing: border-box;
  font-size: 28rpx;
  line-height: 1.3;
  padding: 20rpx;
  border-radius: 10rpx;
  background: #fff;
}
.message .content image.data-v-0b926c58 {
  width: 200rpx;
  height: 200rpx;
}
.message.self.data-v-0b926c58 {
  justify-content: flex-end;
}
.message.self .avatar.data-v-0b926c58 {
  margin: 0 0 0 30rpx;
}
.message.self .msg-box.data-v-0b926c58 {
  display: flex;
  align-items: flex-start;
}
.message.self .content.data-v-0b926c58 {
  position: relative;
}
.message.self .content.data-v-0b926c58::after {
  position: absolute;
  content: "";
  width: 0;
  height: 0;
  border: 16rpx solid transparent;
  border-left: 16rpx solid #fff;
  right: -28rpx;
  top: 24rpx;
}
.message.friend .content.data-v-0b926c58 {
  position: relative;
  word-wrap: break-word;
  word-break: break-all;
}
.message.friend .content.data-v-0b926c58::after {
  position: absolute;
  content: "";
  width: 0;
  height: 0;
  border: 16rpx solid transparent;
  border-right: 16rpx solid #fff;
  left: -28rpx;
  top: 24rpx;
}
.tool.data-v-0b926c58 {
  position: fixed;
  width: 100%;
  min-height: 120rpx;
  left: 0;
  bottom: 0;
  background: #fff;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 20rpx 24rpx 20rpx 24rpx;
  padding-bottom: calc(20rpx + constant(safe-area-inset-bottom) / 2) !important;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom) / 2) !important;
}
.tool .left-icon.data-v-0b926c58 {
  width: 56rpx;
  height: 56rpx;
  margin-right: 10rpx;
}
.tool .input.data-v-0b926c58,
.tool .voice-crl.data-v-0b926c58 {
  background: #eee;
  border-radius: 10rpx;
  height: 70rpx;
  margin-right: 16rpx;
  flex: 1;
  padding: 0 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
}
.tool .thumb.data-v-0b926c58 {
  width: 64rpx;
  height: 64rpx;
}
.tool .voice-crl.data-v-0b926c58 {
  text-align: center;
  line-height: 70rpx;
  font-weight: bold;
}
.send-btn.data-v-0b926c58 {
  width: 0;
  height: 56rpx;
  background-color: #00c75a;
  color: #fff;
  padding: 0;
  font-size: 28rpx;
  border-radius: 12rpx;
  line-height: 56rpx;
  margin-left: 12rpx;
  transition: all 0.3s ease-in;
}
.show-send-btn.data-v-0b926c58 {
  width: 100rpx !important;
}
.audio-animation.data-v-0b926c58 {
  position: fixed;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  z-index: 202410;
  display: flex;
  justify-content: center;
  align-items: center;
}
.audio-animation .text.data-v-0b926c58 {
  text-align: center;
  font-size: 28rpx;
  color: #333;
  margin-top: 60rpx;
}
.audio-animation .audio-wave.data-v-0b926c58 {
  padding: 50rpx;
  /*  声波动画  */
}
.audio-animation .audio-wave .audio-wave-text.data-v-0b926c58 {
  background-color: blue;
  width: 7rpx;
  height: 12rpx;
  margin: 0 6rpx;
  border-radius: 5rpx;
  display: inline-block;
  border: none;
  -webkit-animation: wave-data-v-0b926c58 0.25s ease-in-out;
          animation: wave-data-v-0b926c58 0.25s ease-in-out;
  -webkit-animation-iteration-count: infinite;
          animation-iteration-count: infinite;
  -webkit-animation-direction: alternate;
          animation-direction: alternate;
}
@-webkit-keyframes wave-data-v-0b926c58 {
from {
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
}
to {
    -webkit-transform: scaleY(4);
            transform: scaleY(4);
}
}
@keyframes wave-data-v-0b926c58 {
from {
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
}
to {
    -webkit-transform: scaleY(4);
            transform: scaleY(4);
}
}
.nickname.data-v-0b926c58 {
  font-size: 24rpx;
  margin-bottom: 6rpx;
  color: #a6a6a6;
}
.right-name.data-v-0b926c58 {
  display: flex;
  align-items: center;
}
.time.data-v-0b926c58 {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
}
.time .time-text.data-v-0b926c58 {
  padding: 10rpx;
  font-size: 24rpx;
  color: #333;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 8rpx;
}
.icon-voice.data-v-0b926c58 {
  width: 30rpx !important;
  height: 30rpx;
  margin-left: 12rpx;
}
.rate-90.data-v-0b926c58 {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
  margin-right: 12rpx;
}
.item-center.data-v-0b926c58 {
  display: flex;
  align-items: center;
}
